group:
  keys:
  - ip
  - port
  filter:
    require_any_ids:
    - SSLv2
    - SSLv3
    - TLS1
    - TLS1_1
    - TLS1_2
    - TLS1_3
endpoint:
  fields:
  - display:
      default: ${ip}
      case:
      - ${ip}~^(.+)/$ -> \1
      - ${ip}~^([^/]+)/(.+)$ -> \1 (\2)
  - port:
      default: ${port}
tables:
  protocols:
  - SSLv2:
      default: Disabled
      case: SSLv2~^(offered|supported|yes)\b -> Enabled
  - SSLv3:
      default: Disabled
      case: SSLv3~^(offered|supported|yes)\b -> Enabled
  - TLS1_0:
      default: Disabled
      case: TLS1~^(offered|supported)\b -> Enabled
  - TLS1_1:
      default: Disabled
      case: TLS1_1~^(offered|supported)\b -> Enabled
  - TLS1_2:
      default: Disabled
      case: TLS1_2~^(offered|supported)\b -> Enabled
  - TLS1_3:
      default: Not supported
      case: TLS1_3~^(offered|supported)\b -> Enabled
  ciphers:
  - RC4:
      default: Enabled
      case: RC4~^not vulnerable\b -> Disabled
  - TRIPLE_DES:
      default: Enabled
      case: cipherlist_3DES_IDEA~^not offered\b -> Disabled
  - EXPORT:
      default: Enabled
      case: cipherlist_EXPORT~^not offered\b -> Disabled
  - CBC_OBS:
      default: Disabled
      case: any(cipherlist_LOW|cipherlist_OBSOLETED)~^offered\b -> Enabled
  - PFS:
      default: Disabled
      case: any(FS|cipherlist_STRONG_FS)~^offered\b -> Enabled
  extras:
  - WEAK_DH:
      default: Disabled
      case: LOGJAM~vulnerable -> Enabled
  - TLS_COMPRESSION:
      default: Disabled
      case: CRIME_TLS~compression (enabled|possible) -> Enabled
  - HTTP_COMPRESSION:
      default: Disabled
      case: BREACH~gzip|deflate|compress|br -> Enabled
scope:
  flags:
    any_legacy_enabled: any(SSLv2|SSLv3|TLS1_0|TLS1_1) == Enabled
    all_tls13_missing: all(TLS1_3) != Enabled
    sslv2_enabled: any(SSLv2) == Enabled
    sslv3_enabled: any(SSLv3) == Enabled
    tls10_enabled: any(TLS1_0) == Enabled
    tls11_enabled: any(TLS1_1) == Enabled
    tls12_missing: any(TLS1_2) != Enabled
    tls13_missing: any(TLS1_3) != Enabled
    pfs_missing: any(PFS) != Enabled
    rc4_enabled: any(RC4) == Enabled
    tdes_enabled: any(TRIPLE_DES) == Enabled
    export_enabled: any(EXPORT) == Enabled
    cbc_obsolete_enabled: any(CBC_OBS) == Enabled
    tls_compression_enabled: any(TLS_COMPRESSION) == Enabled
    http_compression_enabled: any(HTTP_COMPRESSION) == Enabled
    any_weak_dh: any(WEAK_DH) == Enabled
