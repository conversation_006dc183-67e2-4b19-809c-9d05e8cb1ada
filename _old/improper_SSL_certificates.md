---
title: "Improper TLS Certificates"
---
## Risk Rating

* **Overall risk:** Low
* **Impact:** Medium
* **Likelihood:** Low

## Description

We found several TLS-enabled services which had problems with their certificates.

| IP Address | Port | Subject         | Issued by            | Signature Algorithm    | Expiration      | Key size (bits) |
| ---------- | ---- | --------------- | -------------------- | ---------------------- | --------------- | --------------- |
| *******    | 443  | `*.*`           | Self-signed          | `SHA256 with RSA (OK)` | `>60 days (OK)` | `1024`          |
| *******    | 443  | `test.com`      | Issued by X          | `MD5-RSA`              | `expired`       | `2048 (OK)`     |

"Identified TLS certificate details"

| Mark        | Description                  |
| ----------- | ---------------------------- |
| Red color   | Insecure certificate setting |
| Green color | No risk identified           |

*Legend of Improper TLS Certificates*

{{% info %}}
If there are many affected hosts, use the description below.

The [`InfraTestssl2Excel`](https://git-inventory-01-prod.crs.corp/crs_projects/infratestssl2excel "InfraTestssl2Excel on GitLab") tool can be used to generate the report attachment from flat JSON files.
{{% /info %}}

We found several TLS-enabled services, which had issues with their certificates:

  - Weak RSA key
  - Weak hashing algorithm
  - Self-signed certificates
  - Expired certificates

For the full list of affected hosts, refer to “Improper TLS Certificates” tab in the attached
spreadsheet.

## Impact

The above mentioned issues can make harder for a legitimate user to
verify the service's identity, or could make executions of attacks, such
as Man-in-the-Middle (MitM) easier for an attacker.

## Likelihood

The attacker would need to be on designated networks (i.e., on the path
between the user and the server) to abuse these certificate issues.
Advanced technical skills are required to intercept, manipulate and
decrypt TLS traffic.

## Recommendation

{{% info %}}
Include only those points that are relevant to the issues in the Description.
{{% /info %}}

We recommend replacing the TLS certificates affected by these issues with
ones which are compliant with the following conditions:

* The certificate is issued by a trusted Certificate Authority (it can be the company’s own CA,
if the root CA certificate is imported into the trusted certificate store of the workstations).
* The certificate is not expired.
* The certificate is signed using a strong hash algorithm (e.g., SHA2 family).
* The certificate keys are at least
    * 2048 bits long in case of RSA, DSA and DH certificates.
    * 256 bits long in case of ECDSA, ECDH certificates. In this case the elliptic curve should be P-256 or P-384.

## CVE References

* NIST SP 800-52 Revision 2: https://csrc.nist.gov/publications/detail/sp/800-52/rev-2/final, published August 2019.

## CWE References

  - `CWE-296`: Improper Following of a Certificate's Chain of Trust
  - `CWE-298`: Improper Validation of Certificate Expiration

## CVSSv3

| CVSS Type | Metrics | Score |
| --------- | ------- | ----- |
| Base      |         |       |

"CVSSv3 Score"
