#!/usr/bin/env python3
import argparse
import os
import sys
import json
import glob
import re
from datetime import datetime
from collections import defaultdict
from jinja2 import Environment, FileSystemLoader

# -------------------------------
# Utilities
# -------------------------------

def load_records(files):
    records = []
    for path in files:
        try:
            with open(path, 'r', encoding='utf-8') as fh:
                data = json.load(fh)
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            # normalize a few fields
                            item.setdefault('id', '')
                            item.setdefault('ip', '')
                            item.setdefault('port', '')
                            item.setdefault('finding', '')
                            records.append(item)
        except Exception as e:
            print(f"[WARN] Failed to read {path}: {e}", file=sys.stderr)
    return records


def group_by(records, key_fields):
    groups = defaultdict(list)
    for r in records:
        key = tuple(r.get(k, '') for k in key_fields)
        groups[key].append(r)
    return groups


# -------------------------------
# Rule engine (tiny DSL)
#   "ID~regex -> output"
#   selector can be e.g.  SSLv2, TLS1_3, or any(service)
#   output may use backrefs (\1), and ${ip}, ${port}
# -------------------------------

_rule_re = re.compile(r"^\s*(?P<sel>[^~]+)~(?P<rx>.+?)\s*->\s*(?P<out>.*)\s*$")
_any_re = re.compile(r"^any\((?P<id>[^)]+)\)$", re.IGNORECASE)


def parse_rule(rule_str):
    m = _rule_re.match(rule_str)
    if not m:
        raise ValueError(f"Invalid rule syntax: {rule_str}")
    sel = m.group('sel').strip()
    rx = m.group('rx').strip()
    out = m.group('out')
    anym = _any_re.match(sel)
    if anym:
        return { 'selector': ('any', anym.group('id').strip()), 'regex': re.compile(rx, re.IGNORECASE), 'out': out }
    else:
        return { 'selector': ('id', sel), 'regex': re.compile(rx, re.IGNORECASE), 'out': out }


def eval_rule(rule, endpoint_records, ip, port):
    sel_type, sel_val = rule['selector']
    rx = rule['regex']
    out = rule['out']

    # Choose candidate findings
    if sel_type == 'any':
        cands = [r for r in endpoint_records if r.get('id') == sel_val]
    else:  # exact id
        cands = [r for r in endpoint_records if r.get('id') == sel_val]

    for r in cands:
        finding = str(r.get('finding', ''))
        m = rx.search(finding)
        if m:
            # Expand backrefs first
            try:
                s = m.expand(out)
            except Exception:
                s = out
            # Then ${ip}/${port}
            s = s.replace('${ip}', ip).replace('${port}', str(port))
            return s
    return None


def compute_flags(cfg, groups):
    flags = {}
    scope = cfg.get('scope', {})
    for flag_item in scope.get('flags', []) or []:
        # flag_item can be {name: { any: "rule" }} or {name: { any: ["rule", ...] }}
        if not isinstance(flag_item, dict):
            continue
        name, spec = next(iter(flag_item.items()))
        value = False
        if isinstance(spec, dict):
            # Only support 'any' at the moment
            rule_spec = spec.get('any')
            if isinstance(rule_spec, list):
                rules = [parse_rule(r) for r in rule_spec]
            elif isinstance(rule_spec, str):
                rules = [parse_rule(rule_spec)]
            else:
                rules = []
            # Evaluate over all endpoints
            for (ip, port), ep in groups.items():
                for rule in rules:
                    if eval_rule(rule, ep, ip, port) is not None:
                        value = True
                        break
                if value:
                    break
        flags[name] = value
    return { 'flags': flags }


def build_tables(cfg, groups):
    tables_out = []
    for table in cfg.get('tables', []) or []:
        title = table.get('title', '')
        cols_spec = table.get('columns', [])
        # Normalize columns: each entry is {header: {default?, case?}}
        norm_cols = []
        for entry in cols_spec:
            if not isinstance(entry, dict):
                continue
            header, spec = next(iter(entry.items()))
            if isinstance(spec, str):
                spec = {'case': spec}
            # case can be str or list
            case = spec.get('case', [])
            if isinstance(case, str):
                case = [case]
            default = spec.get('default', '')
            norm_cols.append({'header': header, 'default': default, 'rules': [parse_rule(r) for r in case]})

        # Build rows
        rows = []
        for (ip, port), ep in groups.items():
            row = []
            for col in norm_cols:
                val = col['default']
                for rule in col['rules']:
                    out = eval_rule(rule, ep, ip, port)
                    if out is not None:
                        val = out
                        break
                row.append(val)
            rows.append(row)

        tables_out.append({
            'title': title,
            'columns': [c['header'] for c in norm_cols],
            'rows': rows,
        })
    return tables_out


def apply_group_filter(cfg, groups):
    # Optional: groups.filter.require_any_ids: ["service", ...]
    filt = (cfg.get('groups') or {}).get('filter') or {}
    req = set((filt.get('require_any_ids') or []))
    if not req:
        return groups
    out = {}
    for k, ep in groups.items():
        ids = {r.get('id') for r in ep}
        if ids.intersection(req):
            out[k] = ep
    return out


# -------------------------------
# Main
# -------------------------------

def main():
    ap = argparse.ArgumentParser(description="reportify-testssl: YAML+Jinja report generator for testssl.sh JSON")
    ap.add_argument('-f', '--file', dest='files', action='append', default=[], help='JSON file (repeatable)')
    ap.add_argument('-d', '--input-dir', help='Directory with JSON files')
    ap.add_argument('--glob', default='*.json', help='Glob for --input-dir (default: *.json)')
    ap.add_argument('-c', '--config', default='config.yaml', help='Config file or a profile directory (default: config.yaml)')
    args = ap.parse_args()

    # Resolve config: directory => <dir>/config.yaml
    if os.path.isdir(args.config):
        args.config = os.path.join(args.config, 'config.yaml')
    config_dir = os.path.dirname(os.path.abspath(args.config))

    # Collect input files
    input_files = []
    if args.files:
        input_files.extend(args.files)
    if args.input_dir:
        pattern = os.path.join(args.input_dir, args.glob)
        input_files.extend(glob.glob(pattern))
    # De-dupe & existence
    input_files = [os.path.abspath(p) for p in input_files if os.path.isfile(p)]
    input_files = sorted(set(input_files))
    if not input_files:
        print("[ERROR] No input JSON files. Use -f FILE or -d DIR.", file=sys.stderr)
        sys.exit(2)

    # Load config YAML
    try:
        import yaml
    except ImportError:
        print("[ERROR] PyYAML is required: pip install pyyaml", file=sys.stderr)
        sys.exit(2)

    with open(args.config, 'r', encoding='utf-8') as cf:
        cfg = yaml.safe_load(cf) or {}

    # Resolve template path relative to config file directory if not absolute
    template_path = cfg.get('template') or 'report.md.j2'
    if not os.path.isabs(template_path):
        template_path = os.path.join(config_dir, template_path)

    # Load and group records
    records = load_records(input_files)
    key_fields = (cfg.get('groups') or {}).get('key') or ['ip', 'port']
    groups = group_by(records, key_fields)
    groups = apply_group_filter(cfg, groups)

    # Build scope flags + tables
    scope = compute_flags(cfg, groups)
    tables = build_tables(cfg, groups)

    # Render
    env = Environment(loader=FileSystemLoader(os.path.dirname(template_path)), autoescape=False, trim_blocks=True, lstrip_blocks=True)
    tpl = env.get_template(os.path.basename(template_path))
    rendered = tpl.render(tables=tables, scope=scope)

    # Output file
    ts = datetime.now().strftime('%Y%m%d-%H%M%S')
    out_path = os.path.abspath(f'output-{ts}.md')
    with open(out_path, 'w', encoding='utf-8') as oh:
        oh.write(rendered.rstrip() + '\n')

    # Also print to stdout
    print(rendered.rstrip())
    print(f"\n[OK] Written: {out_path}")


if __name__ == '__main__':
    main()
