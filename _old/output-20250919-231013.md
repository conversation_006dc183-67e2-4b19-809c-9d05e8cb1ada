### Description
We identified that the servers in scope were enabled to use weak legacy protocols. According to NIST SP 800-52r2, these protocols no longer meet strong cryptography needs. In addition, we found that the recommended TLS 1.3 protocol was not supported.
|IP Address | TCP Port | SSLv2 | SSLv3 | TLS 1.0 | TLS 1.1 | TLS 1.2 | TLS 1.3|
|-----------------------------------------------------------------------------------------------------|| mbh.hu/********** | 443 | Disabled | Disabled | Disabled | Disabled | Disabled | Not supported || mbh.hu/************* | 443 | Disabled | Disabled | Disabled | Disabled | Disabled | Not supported |
: : Identified SSL/TLS protocols

Furthermore, we identified the following insecure SSL/TLS settings on the servers in scope:

|IP Address | TCP Port | RC4 | 3DES | EXPORT | Obsolete CBC | PFS|
|-------------------------------------------------------------------------------------|| mbh.hu/********** | 443 | Disabled | Disabled | Disabled | Disabled | Disabled || mbh.hu/************* | 443 | Disabled | Disabled | Disabled | Disabled | Disabled |
: : Identified SSL/TLS cipher suite properties

### Recommendation
Improve the TLS/SSL configuration of the affected systems by considering the following:
- For SSL/TLS protocols:  - Consider enabling support for the TLS version 1.3 protocol.
- For SSL/TLS cipher suites:  - If possible, disable HTTP compression (BREACH risk) where applicable.
