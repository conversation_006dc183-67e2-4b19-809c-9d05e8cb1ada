
#!/usr/bin/env python3
import argparse, os, sys, json, glob, re
from datetime import datetime
from collections import defaultdict
from jinja2 import Environment, FileSystemLoader

# ---------- tiny DSL ----------
_rule_re = re.compile(r"^\s*(?P<sel>[^~]+)~(?P<rx>.+?)\s*->\s*(?P<out>.*)\s*$")
_any_re  = re.compile(r"^any\((?P<id>[^)]+)\)$", re.IGNORECASE)

def parse_rule(s):
    m = _rule_re.match(s)
    if not m: raise ValueError(f"Invalid rule: {s}")
    sel = m.group("sel").strip()
    rx  = re.compile(m.group("rx").strip(), re.IGNORECASE)
    out = m.group("out")
    m2  = _any_re.match(sel)
    if m2: return {"selector": ("any", m2.group("id").strip()), "regex": rx, "out": out}
    return {"selector": ("id", sel), "regex": rx, "out": out}

def eval_rule(rule, endpoint_records, ip, port):
    sel_type, sel_val = rule["selector"]
    rx, out = rule["regex"], rule["out"]
    if sel_type == "any":
        cands = [r for r in endpoint_records if r.get("id") == sel_val]
    else:
        cands = [r for r in endpoint_records if r.get("id") == sel_val]
    for r in cands:
        finding = str(r.get("finding",""))
        m = rx.search(finding)
        if m:
            try: s = m.expand(out)
            except Exception: s = out
            return s.replace("${ip}", ip).replace("${port}", str(port))
    return None

# ---------- helpers ----------
def load_records(paths):
    out = []
    for p in paths:
        try:
            with open(p,"r",encoding="utf-8") as fh:
                data = json.load(fh)
            if isinstance(data, list):
                for d in data:
                    if isinstance(d, dict):
                        d.setdefault("id","")
                        d.setdefault("ip","")
                        d.setdefault("port","")
                        d.setdefault("finding","")
                        out.append(d)
        except Exception as e:
            print(f"[WARN] cannot read {p}: {e}", file=sys.stderr)
    return out

def group_by(records, keys=("ip","port")):
    groups = defaultdict(list)
    for r in records:
        groups[tuple(r.get(k,"") for k in keys)].append(r)
    return groups

def apply_group_filter(cfg, groups):
    req = set(((cfg.get("groups") or {}).get("filter") or {}).get("require_any_ids") or [])
    if not req: return groups
    out = {}
    for k, ep in groups.items():
        ids = {r.get("id") for r in ep}
        if ids & req: out[k] = ep
    return out

def compute_flags(cfg, groups):
    flags = {}
    for item in ((cfg.get("scope") or {}).get("flags") or []):
        if not isinstance(item, dict): continue
        name, spec = next(iter(item.items()))
        val = False
        rspec = isinstance(spec, dict) and spec.get("any")
        rules = rspec if isinstance(rspec, list) else ([rspec] if isinstance(rspec, str) else [])
        rules = [parse_rule(r) for r in rules]
        for (ip, port), ep in groups.items():
            for rule in rules:
                if eval_rule(rule, ep, ip, port) is not None:
                    val = True; break
            if val: break
        flags[name] = val
    return {"flags": flags}

def build_table_rows(cfg, groups):
    tables_cfg = cfg.get("tables") or []
    table_rows = {}
    for t in tables_cfg:
        key = t.get("key")
        fields_spec = t.get("fields") or {}
        # normalize field specs
        fdefs = {}
        for fname, spec in fields_spec.items():
            if isinstance(spec, str):
                spec = {"case": [spec]}
            elif isinstance(spec, list):
                spec = {"case": spec}
            default = spec.get("default","")
            case = spec.get("case", [])
            if isinstance(case, str): case = [case]
            fdefs[fname] = {"default": default, "rules": [parse_rule(r) for r in case]}
        # rows
        rows = []
        for (ip, port), ep in groups.items():
            row = {}
            for fname, fdef in fdefs.items():
                val = fdef["default"]
                for rule in fdef["rules"]:
                    out = eval_rule(rule, ep, ip, port)
                    if out is not None:
                        val = out; break
                row[fname] = val
            rows.append(row)
        table_rows[key] = rows
    return table_rows

def main():
    ap = argparse.ArgumentParser(description="reportify-testssl")
    ap.add_argument("-f","--file", dest="files", action="append", default=[], help="Input JSON file (repeatable)")
    ap.add_argument("-d","--input-dir", help="Directory of JSON files")
    ap.add_argument("--glob", default="*.json", help="Glob when using -d (default: *.json)")
    ap.add_argument("-c","--config", default="config.yaml", help="Config file or profile dir (default: config.yaml)")
    args = ap.parse_args()

    # resolve config
    if os.path.isdir(args.config):
        args.config = os.path.join(args.config, "config.yaml")
    cfg_dir = os.path.dirname(os.path.abspath(args.config))

    # gather inputs
    files = []
    if args.files: files.extend(args.files)
    if args.input_dir: files.extend(glob.glob(os.path.join(args.input_dir, args.glob)))
    files = [os.path.abspath(p) for p in files if os.path.isfile(p)]
    files = sorted(set(files))
    if not files:
        print("[ERROR] No input JSONs. Use -f FILE or -d DIR.", file=sys.stderr)
        sys.exit(2)

    try:
        import yaml
    except ImportError:
        print("[ERROR] PyYAML required: pip install pyyaml", file=sys.stderr)
        sys.exit(2)
    with open(args.config,"r",encoding="utf-8") as cf:
        cfg = yaml.safe_load(cf) or {}

    # load & group
    recs = load_records(files)
    key = (cfg.get("groups") or {}).get("key") or ["ip","port"]
    groups = group_by(recs, key)
    groups = apply_group_filter(cfg, groups)

    scope = compute_flags(cfg, groups)
    table_rows = build_table_rows(cfg, groups)

    # render
    template_path = cfg.get("template") or "report.md.j2"
    if not os.path.isabs(template_path):
        template_path = os.path.join(cfg_dir, template_path)
    env = Environment(loader=FileSystemLoader(os.path.dirname(template_path)), autoescape=False, trim_blocks=True, lstrip_blocks=True)
    tpl = env.get_template(os.path.basename(template_path))
    md = tpl.render(scope=scope, table_rows=table_rows)

    ts = datetime.now().strftime("%Y%m%d-%H%M%S")
    outp = os.path.abspath(f"output-{ts}.md")
    with open(outp, "w", encoding="utf-8") as fh:
        fh.write(md.rstrip() + "\n")

    print(md.rstrip())
    print(f"\n[OK] Written: {outp}")

if __name__ == "__main__":
    main()
