template: report.md.j2

groups:
  key: [ip, port]
  filter:
    require_any_ids: ['service']

scope:
  flags:
    - sslv2_enabled:
        any: 'SSLv2~\b(offered|supported|yes)\b -> true'
    - sslv3_enabled:
        any: 'SSLv3~\b(offered|supported|yes)\b -> true'
    - tls10_enabled:
        any: 'TLS1~\b(offered|supported)\b -> true'
    - tls11_enabled:
        any: 'TLS1_1~\b(offered|supported)\b -> true'
    - tls12_missing:
        any: 'TLS1_2~\bnot offered\b -> true'
    - tls13_missing:
        any: 'TLS1_3~\bnot offered\b|downgraded -> true'
    - export_ciphers:
        any: 'cipherlist_EXPORT~\b(offered|supported|yes)\b -> true'
    - des3_ciphers:
        any: 'cipherlist_3DES_IDEA~\b(offered|supported|yes)\b -> true'
    - low_ciphers:
        any: 'cipherlist_LOW~\b(offered|supported|yes)\b -> true'
    - null_ciphers:
        any: 'cipherlist_NULL~\b(offered|supported|yes)\b -> true'
    - anull_ciphers:
        any: 'cipherlist_aNULL~\b(offered|supported|yes)\b -> true'
    - obsolete_cbc:
        any: 'cipherlist_OBSOLETED~\b(offered|supported|yes)\b -> true'
    - pfs_missing_any:
        any: 'FS~\bnot offered\b|\bno\b -> true'
    - crime_risk:
        any: 'CRIME_TLS~\bvulnerable\b|compression.+enabled -> true'
    - breach_risk:
        any: 'BREACH~\bvulnerable\b|compression -> true'
    - dh_weak_1024:
        any: 'LOGJAM~\b1024\b|\bweak\b|\bexport\b -> true'
    - legacy_protocols_any:
        any:
          - 'SSLv2~\b(offered|supported|yes)\b -> true'
          - 'SSLv3~\b(offered|supported|yes)\b -> true'
          - 'TLS1~\b(offered|supported)\b -> true'
          - 'TLS1_1~\b(offered|supported)\b -> true'

tables:

  - key: protocols
    fields:
      ip:
        case: 'any(service)~.* -> ${ip}'
      port:
        case: 'any(service)~.* -> ${port}'
      sslv2:
        default: 'Disabled'
        case:
          - 'SSLv2~\b(offered|supported|yes)\b -> Enabled'
          - 'SSLv2~\bnot offered\b|\bno\b -> Disabled'
      sslv3:
        default: 'Disabled'
        case:
          - 'SSLv3~\b(offered|supported|yes)\b -> Enabled'
          - 'SSLv3~\bnot offered\b|\bno\b -> Disabled'
      tls10:
        default: 'Disabled'
        case:
          - 'TLS1~\b(offered|supported)\b -> Enabled'
          - 'TLS1~\bnot offered\b|\bno\b -> Disabled'
      tls11:
        default: 'Disabled'
        case:
          - 'TLS1_1~\b(offered|supported)\b -> Enabled'
          - 'TLS1_1~\bnot offered\b|\bno\b -> Disabled'
      tls12:
        default: 'Disabled'
        case:
          - 'TLS1_2~\b(offered|supported)\b -> Enabled'
          - 'TLS1_2~\bnot offered\b|\bno\b -> Disabled'
      tls13:
        default: 'Not supported'
        case:
          - 'TLS1_3~\b(offered|supported)\b -> Enabled'
          - 'TLS1_3~\bnot offered\b|downgraded -> Not supported'

  - key: ciphers
    fields:
      ip:
        case: 'any(service)~.* -> ${ip}'
      port:
        case: 'any(service)~.* -> ${port}'
      rc4:
        default: 'Disabled'
        case:
          - 'RC4~\bnot vulnerable\b -> Disabled'
          - 'RC4~\bvulnerable\b|\boffered\b|\bsupported\b|\byes\b -> Enabled'
      des3:
        default: 'Disabled'
        case:
          - 'cipherlist_3DES_IDEA~\b(offered|supported|yes)\b -> Enabled'
          - 'cipherlist_3DES_IDEA~\bnot offered\b|\bno\b -> Disabled'
      export:
        default: 'Disabled'
        case:
          - 'cipherlist_EXPORT~\b(offered|supported|yes)\b -> Enabled'
          - 'cipherlist_EXPORT~\bnot offered\b|\bno\b -> Disabled'
      obsolete_cbc:
        default: 'Disabled'
        case:
          - 'cipherlist_OBSOLETED~\b(offered|supported|yes)\b -> Enabled'
          - 'cipherlist_OBSOLETED~\bnot offered\b|\bno\b -> Disabled'
      pfs:
        default: 'Disabled'
        case:
          - 'FS~\b(offered|supported|yes)\b -> Enabled'
          - 'FS~\bnot offered\b|\bno\b -> Disabled'
