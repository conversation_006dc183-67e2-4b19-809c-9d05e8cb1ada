
template: report.md.j2

groups:
  key: [ip, port]

tables:
  - key: certs
    fields:
      ip:
        case: 'any(service)~.* -> ${ip}'
      port:
        case: 'any(service)~.* -> ${port}'
      subject:
        case:
          - 'cert_commonName~(.+) -> `\1`'
          - 'cert_subjectAltName~([^ ]+) -> `\1`'
      issued_by:
        case:
          - 'cert_chain_of_trust~self signed -> Self-signed'
          - 'cert_caIssuers~(.+) -> \1'
      sigalg:
        case:
          - 'cert_signatureAlgorithm~(.+sha(?:256|384|512).+) -> `\1 (OK)`'
          - 'cert_signatureAlgorithm~(.+) -> `\1`'
      expiration:
        case:
          - 'cert_expirationStatus~\bexpired\b -> `expired`'
          - 'cert_expirationStatus~>=\s*60\s*days -> `>60 days (OK)`'
          - 'cert_expirationStatus~(.+) -> `\1`'
      key_bits:
        case:
          - 'cert_keySize~RSA\s+(2048|3072|4096)\s+bits -> `\1 (OK)`'
          - 'cert_keySize~EC\s+(256|384|521)\s+bits -> `\1 (OK)`'
          - 'cert_keySize~.*?(\d+)\s+bits -> `\1`'
