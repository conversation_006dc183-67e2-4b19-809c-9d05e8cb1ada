
### Description
We identified that the servers in scope were enabled to use weak legacy protocols. According to NIST SP 800-52r2, these protocols no longer meet strong cryptography needs. In addition, we found that the recommended TLS 1.3 protocol was not supported.
| IP Address | TCP Port | SSLv2 | SSLv3 | TLS 1.0 | TLS 1.1 | TLS 1.2 | TLS 1.3 |
|------------|----------|-------|-------|---------|---------|---------|---------|| mbh.hu/********** | 443 |  |  |  |  |  |  || mbh.hu/************* | 443 |  |  |  |  |  |  |
: : Identified SSL/TLS protocols

Furthermore, we identified the following insecure SSL/TLS settings on the servers in scope:

| IP Address | TCP Port | RC4 | 3DES | EXPORT | Obsolete CBC | PFS |
|------------|----------|-----|------|--------|--------------|-----|| mbh.hu/********** | 443 |  |  |  |  |  || mbh.hu/************* | 443 |  |  |  |  |  |
: : Identified SSL/TLS cipher suite properties

We also identified that at least one service was using a 1024-bit Diffie-Hellman group, which could make the server vulnerable to the Logjam attack.

### Recommendation
Improve the TLS/SSL configuration of the affected systems by considering the following:
- For SSL/TLS protocols:  - Disable support for the SSL version 2 protocol due to known weaknesses and DROWN exposure.  - If possible, disable support for SSL version 3; it exposes users to the POODLE attack.  - If possible, disable TLS 1.0; it exposes users to the BEAST attack.  - If possible, disable TLS 1.1; it is deprecated and may expose users to cryptographic risks.  - Consider enabling support for the TLS version 1.3 protocol.
- For SSL/TLS cipher suites:  - Disable weak/legacy cipher suites (EXPORT, 3DES/IDEA, LOW/NULL/aNULL, obsolete CBC).
