Traceback (most recent call last):
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 402, in <module>
    main()
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 387, in main
    md = render_markdown(ctx, template_path)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 314, in render_markdown
    return tpl.render(**context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/Users/<USER>/Development/reportify-testssl/report.md.j2", line 2, in top-level template code
    {% if scope.flags.any_legacy_enabled and scope.flags.tls13_missing -%}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'any_legacy_enabled'
