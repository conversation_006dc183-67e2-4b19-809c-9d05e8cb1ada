#!/usr/bin/env python3
# reportify-testssl - render markdown report from testssl.sh JSON using a YAML-configured Jinja template
# CLI:
#   -f/--file <file>   (repeatable) testssl JSON file(s)
#   -d/--input-dir DIR (--glob pattern) directory of JSONs
#   -c/--config config.yaml (defaults to config.yaml)
#
# Output: output-YYYYMMDD-HHMMSS.md (saved in CWD) and also printed to stdout.

import argparse
import datetime as _dt
import glob
import json
import os
import re
import sys
from collections import defaultdict
from typing import Any, Dict, List, Tuple

import yaml
from jinja2 import Environment, FileSystemLoader, StrictUndefined

# ----------------------------- JSON / grouping helpers -----------------------------

def load_json(path: str) -> List[Dict[str, Any]]:
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)

def bucket_key(item: Dict[str, Any], keys: List[str]) -> Tuple:
    return tuple(item.get(k) for k in keys)

def group_items(items: List[Dict[str, Any]], keys: List[str]) -> List[List[Dict[str, Any]]]:
    groups: Dict[Tuple, List[Dict[str, Any]]] = defaultdict(list)
    for it in items:
        groups[bucket_key(it, keys)].append(it)
    return list(groups.values())

def get_finding(bucket: List[Dict[str, Any]], fid: str) -> str:
    for it in bucket:
        if it.get("id") == fid:
            return it.get("finding", "")
    return ""

def match_any_ids(bucket: List[Dict[str, Any]], ids: List[str], rx: str):
    """
    Return (True, matchobj) if any id's finding matches rx; else (False, None).
    """
    cre = re.compile(rx, re.IGNORECASE | re.MULTILINE)
    for it in bucket:
        if it.get("id") in ids:
            m = cre.search(it.get("finding", "") or "")
            if m:
                return True, m
    return False, None

def apply_cases_for_finding(bucket: List[Dict[str, Any]], default_value: Any, cases: List[str]):
    """
    Rule format: "ID~regex -> value"  or  "any(A|B|C)~regex -> value"
    - RHS supports ${ip}, ${port} placeholders (from the first item in the bucket).
    - RHS supports regex backrefs like \\1, \\2 via match.expand().
    """
    val = default_value
    for rule in cases:
        if not isinstance(rule, str):
            continue
        m = re.match(r'^(any\(([^)]+)\)|[A-Za-z0-9_#<>\s/.\-]+)~(.+?)\s*->\s*(.+)$', rule)
        if not m:
            continue
        lhs, ids_part, rx, out = m.group(1), m.group(2), m.group(3), m.group(4)

        # placeholders from bucket context
        ip0 = str(bucket[0].get("ip", "")) if bucket and isinstance(bucket[0], dict) else ""
        port0 = str(bucket[0].get("port", "")) if bucket and isinstance(bucket[0], dict) else ""
        out = out.replace("${ip}", ip0).replace("${port}", port0)

        if lhs.startswith("any("):
            ids = [s.strip() for s in (ids_part or "").split("|") if s.strip()]
            ok, mm = match_any_ids(bucket, ids, rx)
            if ok:
                try:
                    val = mm.expand(out) if mm else out
                except Exception:
                    val = out
                break
        else:
            fid = lhs.strip()
            txt = get_finding(bucket, fid)
            mm = re.search(rx, txt or "", re.IGNORECASE | re.MULTILINE)
            if mm:
                try:
                    val = mm.expand(out)
                except Exception:
                    val = out
                break
    return val

def normalize_field_spec(spec: Any) -> Dict[str, Any]:
    """
    Accepts minimal / tree-style YAML (string or mapping). Returns dict with keys:
      name, default, case (list or [])
    String spec means column title equals the string; default empty; no cases.
    """
    if isinstance(spec, str):
        return {"name": spec, "default": "", "case": []}
    if isinstance(spec, dict):
        # spec is like: { "SSLv2": { default: "Disabled", case: "..." } } or tree-style:
        # - SSLv2:
        #     default: Disabled
        #     case: '...'
        if len(spec) == 1 and list(spec.keys())[0] not in ("name", "default", "case"):
            # wrapped: {"SSLv2": {...}}
            key = list(spec.keys())[0]
            inner = spec[key] or {}
            default = inner.get("default", "")
            cases = inner.get("case", [])
            if isinstance(cases, str):
                cases = [cases]
            return {"name": key, "default": default, "case": cases}
        # already a flat mapping
        name = spec.get("name") or ""
        default = spec.get("default", "")
        cases = spec.get("case", [])
        if isinstance(cases, str):
            cases = [cases]
        return {"name": name, "default": default, "case": cases}
    # fallback
    return {"name": str(spec), "default": "", "case": []}

# ----------------------------- scope / flags -----------------------------

def compute_scope_flags(endpoints: List[Dict[str, Any]], cfg: Dict[str, Any]) -> Dict[str, Any]:
    """
    Evaluate booleans defined under cfg['scope']['flags'].
    Supports both old format (dict) and new format (list).
    Old format example:
      scope:
        flags:
          tls13_missing: any(TLS1_3) != Enabled
    New format example:
      scope:
        flags:
          - tls13_missing:
              any: 'TLS1_3~\\bnot offered\\b -> true'
    'any' / 'all' can be string or list of strings, using the rule grammar against each endpoint bucket.
    """
    flags_out: Dict[str, Any] = {}
    flags_def = (((cfg or {}).get("scope") or {}).get("flags")) or []

    # Handle both old format (dict) and new format (list)
    if isinstance(flags_def, dict):
        # Old format: convert to new format
        # For now, we'll implement a simple conversion that handles the basic cases
        # The old format uses expressions like "any(SSLv2) == Enabled"
        # We need to convert these to the new rule format
        for flag_name, flag_expr in flags_def.items():
            # For now, just set all flags to False as a fallback
            # TODO: Implement proper conversion from old format expressions
            flags_out[flag_name] = False
        return flags_out

    # New format: process as list
    for flag_entry in flags_def:
        if not isinstance(flag_entry, dict) or not flag_entry:
            continue
        name = list(flag_entry.keys())[0]
        body = flag_entry[name] or {}

        def _normalize_rules(val):
            if not val:
                return []
            if isinstance(val, str):
                return [val]
            if isinstance(val, list):
                return [v for v in val if isinstance(v, str)]
            return []

        any_rules = _normalize_rules(body.get("any"))
        all_rules = _normalize_rules(body.get("all"))
        # Evaluate any_rules: true if any endpoint satisfies ANY of the listed rules
        any_ok = False
        if any_rules:
            for ep in endpoints:
                bucket = ep.get("_bucket", [])
                for rule in any_rules:
                    v = apply_cases_for_finding(bucket, False, [rule])
                    if str(v).lower() in ("true", "1", "yes", "enabled"):
                        any_ok = True
                        break
                if any_ok:
                    break
        # Evaluate all_rules: true if all endpoints satisfy at least one rule in all_rules
        all_ok = True if all_rules else False
        if all_rules:
            for ep in endpoints:
                bucket = ep.get("_bucket", [])
                ep_ok = False
                for rule in all_rules:
                    v = apply_cases_for_finding(bucket, False, [rule])
                    if str(v).lower() in ("true", "1", "yes", "enabled"):
                        ep_ok = True
                        break
                if not ep_ok:
                    all_ok = False
                    break
        result = None
        if any_rules and all_rules:
            result = any_ok and all_ok
        elif any_rules:
            result = any_ok
        elif all_rules:
            result = all_ok
        else:
            result = False
        flags_out[name] = result
    return flags_out

# ----------------------------- table construction -----------------------------

def build_endpoint_row(bucket: List[Dict[str, Any]], fields_spec: List[Any]) -> Dict[str, Any]:
    row: Dict[str, Any] = {}
    for spec in fields_spec:
        fs = normalize_field_spec(spec)
        name = fs["name"]
        default = fs.get("default", "")
        cases = fs.get("case", []) or []
        if cases:
            val = apply_cases_for_finding(bucket, default, cases)
        else:
            # Handle variable substitution in default values
            val = default
            if "${ip}" in val or "${port}" in val:
                ip0 = str(bucket[0].get("ip", "")) if bucket and isinstance(bucket[0], dict) else ""
                port0 = str(bucket[0].get("port", "")) if bucket and isinstance(bucket[0], dict) else ""
                val = val.replace("${ip}", ip0).replace("${port}", port0)
        row[name] = val
    return row

def filter_bucket(bucket: List[Dict[str, Any]], table_cfg: Dict[str, Any]) -> bool:
    flt = (table_cfg or {}).get("filter") or {}
    req_ids = flt.get("require_any_ids") or []
    if req_ids:
        have = any((it.get("id") in req_ids) for it in bucket)
        if not have:
            return False
    return True

def build_endpoints(items: List[Dict[str, Any]], cfg: Dict[str, Any]) -> List[Dict[str, Any]]:
    grp = (cfg.get("groups") or {}).get("key") or ["ip", "port"]
    buckets = group_items(items, grp)
    endpoints: List[Dict[str, Any]] = []
    endpoint_fields = ((cfg.get("endpoint") or {}).get("fields")) or []
    # Optional group-level filter
    group_filter = ((cfg.get("groups") or {}).get("filter")) or {}
    require_ids = group_filter.get("require_any_ids") or []

    # Get table definitions to populate endpoint data
    tables_cfg = cfg.get("tables") or []

    for b in buckets:
        if require_ids:
            if not any((it.get("id") in require_ids) for it in b):
                continue
        ep_row = build_endpoint_row(b, endpoint_fields)
        # Attach raw values for convenience
        ip = b[0].get("ip", "") if b else ""
        port = b[0].get("port", "") if b else ""
        ep_row["_bucket"] = b
        ep_row["_ip"] = ip
        ep_row["_port"] = port

        # Populate table data in endpoint for template access
        if isinstance(tables_cfg, dict):
            # Old format: tables_cfg is {"protocols": [...], "ciphers": [...], ...}
            for table_name, table_fields in tables_cfg.items():
                table_data = {}
                if isinstance(table_fields, list):
                    for field_spec in table_fields:
                        if isinstance(field_spec, dict):
                            for field_name, field_config in field_spec.items():
                                # Compute field value using the same logic as build_tables
                                fs = normalize_field_spec({field_name: field_config})
                                default = fs.get("default", "")
                                cases = fs.get("case", [])
                                if cases:
                                    val = apply_cases_for_finding(b, default, cases)
                                else:
                                    # Handle variable substitution in default values
                                    val = default
                                    if "${ip}" in val or "${port}" in val:
                                        ip0 = str(b[0].get("ip", "")) if b and isinstance(b[0], dict) else ""
                                        port0 = str(b[0].get("port", "")) if b and isinstance(b[0], dict) else ""
                                        val = val.replace("${ip}", ip0).replace("${port}", port0)
                                table_data[field_name] = val
                ep_row[table_name] = table_data

        endpoints.append(ep_row)
    return endpoints

def build_tables(endpoints: List[Dict[str, Any]], cfg: Dict[str, Any]) -> List[Dict[str, Any]]:
    out_tables: List[Dict[str, Any]] = []
    tables_cfg = cfg.get("tables") or []

    # Handle both old format (dict) and new format (list)
    if isinstance(tables_cfg, dict):
        # Old format: convert dict to list format
        tables_list = []
        for table_key, table_fields in tables_cfg.items():
            # Convert old format to new format
            columns = []
            if isinstance(table_fields, list):
                # table_fields is a list of field specs
                for field_spec in table_fields:
                    if isinstance(field_spec, dict):
                        # Each field_spec is like {"SSLv2": {"default": "Disabled", "case": "..."}}
                        for field_name, field_config in field_spec.items():
                            columns.append({field_name: field_config})
            tables_list.append({
                "title": table_key.replace("_", " ").title(),
                "columns": columns,
                "rows": {"source": "endpoints"}
            })
        tables_cfg = tables_list

    for tbl in tables_cfg:
        title = tbl.get("title") or ""
        cols_spec = tbl.get("columns") or []
        rows_def = tbl.get("rows") or {}
        source = rows_def.get("source", "endpoints")
        if source != "endpoints":
            continue  # only endpoints supported for now
        # prepare normalized column specs
        norm_cols = [normalize_field_spec(c) for c in cols_spec]
        col_names = [c["name"] for c in norm_cols]
        rows = []
        for ep in endpoints:
            bucket = ep.get("_bucket", [])
            # row-level filter based on table filter
            if not filter_bucket(bucket, tbl):
                continue
            row_vals = []
            for cs in norm_cols:
                name = cs["name"]
                default = cs.get("default", "")
                cases = cs.get("case", [])
                if cases:
                    val = apply_cases_for_finding(bucket, default, cases)
                else:
                    # try pull from endpoint precomputed fields
                    val = ep.get(name, default)
                row_vals.append(val)
            rows.append(row_vals)
        out_tables.append({"title": title, "columns": col_names, "rows": rows})
    return out_tables

# ----------------------------- rendering -----------------------------

def render_markdown(context: Dict[str, Any], template_path: str) -> str:
    tpl_dir = os.path.dirname(template_path) or "."
    tpl_file = os.path.basename(template_path)
    env = Environment(
        loader=FileSystemLoader(tpl_dir),
        undefined=StrictUndefined,
        trim_blocks=True,
        lstrip_blocks=True,
    )
    tpl = env.get_template(tpl_file)
    return tpl.render(**context)

# ----------------------------- main -----------------------------

def main():
    ap = argparse.ArgumentParser(description="reportify-testssl - render markdown report from testssl JSON")
    ap.add_argument("-f", "--file", action="append", help="testssl JSON file (repeatable)")
    ap.add_argument("-d", "--input-dir", help="Directory containing testssl JSON files")
    ap.add_argument("--glob", default="*.json", help="Glob pattern for --input-dir (default: *.json)")
    ap.add_argument("-c", "--config", default="config.yaml", help="YAML config (default: config.yaml)")
    args = ap.parse_args()

    # Collect input files
    input_files: List[str] = []
    if args.file:
        input_files.extend(args.file)
    if args.input_dir:
        pattern = os.path.join(args.input_dir, args.glob)
        input_files.extend(sorted(glob.glob(pattern)))
    # De-duplicate preserving order
    seen = set()
    unique_files = []
    for f in input_files:
        if f not in seen:
            seen.add(f)
            unique_files.append(f)
    input_files = unique_files
    if not input_files:
        print("No input files provided. Use -f/--file and/or -d/--input-dir.", file=sys.stderr)
        sys.exit(2)

    # Load config
    
    # If -c points to a directory, treat it as a bundle and use <dir>/config.yaml
    if os.path.isdir(args.config):
        args.config = os.path.join(args.config, "config.yaml")
    config_dir = os.path.dirname(os.path.abspath(args.config))
    with open(args.config, "r", encoding="utf-8") as cf:
        cfg = yaml.safe_load(cf) or {}

    # Load items from all files
    items: List[Dict[str, Any]] = []
    for fp in input_files:
        try:
            items.extend(load_json(fp))
        except Exception as e:
            print(f"Warning: failed to load {fp}: {e}", file=sys.stderr)

    if not items:
        print("No items parsed from input JSONs.", file=sys.stderr)
        sys.exit(3)

    # Build endpoints and tables
    endpoints = build_endpoints(items, cfg)
    scope_flags = compute_scope_flags(endpoints, cfg)
    tables = build_tables(endpoints, cfg)

    # Context for template
    ctx = {
        "now": _dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "endpoints": endpoints,
        "tables": tables,
        "scope": {"flags": scope_flags},
        # expose cfg if template wants to peek
        "config": cfg,
    }

    # Template path from config (or default)
    template_path = cfg.get("template") or "report.md.j2"
    if not os.path.isabs(template_path):
        template_path = os.path.join(config_dir, template_path)

    # Render
    md = render_markdown(ctx, template_path)

    # Save
    ts = _dt.datetime.now().strftime("%Y%m%d-%H%M%S")
    out_name = f"output-{ts}.md"
    print(f"Saving output to: {out_name}", file=sys.stderr)
    print(f"Number of endpoints: {len(endpoints)}", file=sys.stderr)
    print(f"Number of tables: {len(tables)}", file=sys.stderr)
    with open(out_name, "w", encoding="utf-8") as f:
        f.write(md)

    # Print markdown to stdout (generic)
    print(md)

if __name__ == "__main__":
    main()
