Traceback (most recent call last):
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 420, in <module>
    main()
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 405, in main
    md = render_markdown(ctx, template_path)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Development/reportify-testssl/engine.py", line 332, in render_markdown
    return tpl.render(**context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/Users/<USER>/Development/reportify-testssl/report.md.j2", line 15, in top-level template code
    | {{ ep.display }} | {{ ep.port }} | {{ ep.protocols.SSLv2 }} | {{ ep.protocols.SSLv3 }} | {{ ep.protocols.TLS1_0 }} | {{ ep.protocols.TLS1_1 }} | {{ ep.protocols.TLS1_2 }} | {{ ep.protocols.TLS1_3 }} |
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/jinja2/environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'protocols'
