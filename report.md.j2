### Description
{% if scope.flags.any_legacy_enabled and scope.flags.tls13_missing -%}
We identified that the servers in scope were enabled to use weak legacy protocols. According to NIST SP 800-52r2, these protocols no longer meet strong cryptography needs. TLS 1.3 was also not supported.
{%- elif scope.flags.any_legacy_enabled -%}
We identified that the servers in scope were enabled to use weak legacy protocols. According to NIST SP 800-52r2, these protocols no longer meet strong cryptography needs.
{%- elif scope.flags.tls13_missing -%}
We identified that the servers in scope did not support TLS 1.3.
{%- else -%}
We identified no legacy protocol usage and TLS 1.3 support status is satisfactory.
{%- endif %}

| IP Address | TCP Port | SSLv2 | SSLv3 | TLS 1.0 | TLS 1.1 | TLS 1.2 | TLS 1.3 |
|------------|----------|-------|-------|---------|---------|---------|---------|
{%- for ep in endpoints %}
| {{ ep.display }} | {{ ep.port }} | {{ ep.protocols.SSLv2 }} | {{ ep.protocols.SSLv3 }} | {{ ep.protocols.TLS1_0 }} | {{ ep.protocols.TLS1_1 }} | {{ ep.protocols.TLS1_2 }} | {{ ep.protocols.TLS1_3 }} |
{%- endfor %}

: : Identified SSL/TLS protocols

| IP Address | TCP Port | RC4 | 3DES | EXPORT | Obsolete CBC | PFS |
|------------|----------|-----|------|--------|--------------|-----|
{%- for ep in endpoints %}
| {{ ep.display }} | {{ ep.port }} | {{ ep.ciphers.RC4 }} | {{ ep.ciphers.TRIPLE_DES }} | {{ ep.ciphers.EXPORT }} | {{ ep.ciphers.CBC_OBS }} | {{ ep.ciphers.PFS }} |
{%- endfor %}

: : Identified SSL/TLS cipher properties

### Recommendation
Improve the TLS/SSL configuration of the affected systems by considering the following:
- For SSL/TLS protocols:
{%- if scope.flags.sslv2_enabled %}
- Disable support for the SSL version 2 protocol due to known weaknesses and exposure to DROWN.
{%- endif %}
{%- if scope.flags.sslv3_enabled %}
- Investigate whether SSL version 3 is required; if possible, disable it (POODLE).
{%- endif %}
{%- if scope.flags.tls10_enabled %}
- Investigate whether TLS 1.0 is required; if possible, disable it (BEAST).
{%- endif %}
{%- if scope.flags.tls11_enabled %}
- Investigate whether TLS 1.1 is required; if possible, disable it (deprecated).
{%- endif %}
{%- if scope.flags.tls12_missing %}
- Enable support for the TLS version 1.2 protocol.
{%- endif %}
{%- if scope.flags.tls13_missing %}
- Consider enabling support for the TLS version 1.3 protocol.
{%- endif %}

- For SSL/TLS cipher suites:
{%- if scope.flags.pfs_missing %}
- Enable and prioritize Perfect Forward Secrecy (ECDHE/DHE).
{%- endif %}
{%- if scope.flags.rc4_enabled %}
- Disable RC4 cipher suites.
{%- endif %}
{%- if scope.flags.tdes_enabled %}
- Disable 3DES cipher suites.
{%- endif %}
{%- if scope.flags.export_enabled %}
- Disable EXPORT-grade cipher suites.
{%- endif %}
{%- if scope.flags.cbc_obsolete_enabled %}
- Disable obsolete CBC cipher suites.
{%- endif %}
{%- if scope.flags.tls_compression_enabled %}
- Disable TLS compression to mitigate CRIME.
{%- endif %}
{%- if scope.flags.http_compression_enabled %}
- Disable HTTP compression where feasible to reduce BREACH risk.
{%- endif %}
{%- if scope.flags.any_weak_dh %}
- Use a unique Diffie-Hellman group of at least 2048 bits (mitigates Logjam).
{%- endif %}
