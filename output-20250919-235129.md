### Description
We identified no legacy protocol usage and TLS 1.3 support status is satisfactory.
| IP Address | TCP Port | SSLv2 | SSLv3 | TLS 1.0 | TLS 1.1 | TLS 1.2 | TLS 1.3 |
|------------|----------|-------|-------|---------|---------|---------|---------|| ${ip} | ${port} | Disabled | Disabled | Disabled | Disabled | Disabled | Not supported || ${ip} | ${port} | Disabled | Disabled | Disabled | Disabled | Enabled | Not supported || ${ip} | ${port} | Disabled | Disabled | Disabled | Disabled | Enabled | Not supported |
: : Identified SSL/TLS protocols

| IP Address | TCP Port | RC4 | 3DES | EXPORT | Obsolete CBC | PFS |
|------------|----------|-----|------|--------|--------------|-----|| ${ip} | ${port} | Enabled | Enabled | Enabled | Disabled | Disabled || ${ip} | ${port} | Disabled | Disabled | Disabled | Disabled | Enabled || ${ip} | ${port} | Disabled | Disabled | Disabled | Disabled | Enabled |
: : Identified SSL/TLS cipher properties

### Recommendation
Improve the TLS/SSL configuration of the affected systems by considering the following:
- For SSL/TLS protocols:
- For SSL/TLS cipher suites: