groups:
  key: [ip, port]

tables:
  - title: "TLS Certificate Issues"
    columns:
      - IP Address:
          case:
            - "any(service)~.* -> ${ip}"
      - Port:
          case:
            - "any(service)~.* -> ${port}"
      - Subject:
          case:
            - 'cert_commonName~(.+) -> `$1`'
      - Issued by:
          case:
            - 'cert_chain_of_trust~self signed -> Self-signed'
            - 'cert_caIssuers~(.+) -> $1'
      - Signature Algorithm:
          case:
            - 'cert_signatureAlgorithm~(.+sha(?:256|384|512).+) -> `$1 (OK)`'
            - 'cert_signatureAlgorithm~(.+) -> `$1`'
      - Expiration:
          case:
            - 'cert_expirationStatus~\bexpired\b -> `expired`'
            - 'cert_expirationStatus~>=\s*60\s*days -> `>60 days (OK)`'
            - 'cert_expirationStatus~(.+) -> `$1`'
      - Key size (bits):
          case:
            - 'cert_keySize~RSA\s+(2048|3072|4096)\s+bits -> `$1 (OK)`'
            - 'cert_keySize~EC\s+(256|384|521)\s+bits -> `$1 (OK)`'
            - 'cert_keySize~.*?(\d+)\s+bits -> `$1`'
