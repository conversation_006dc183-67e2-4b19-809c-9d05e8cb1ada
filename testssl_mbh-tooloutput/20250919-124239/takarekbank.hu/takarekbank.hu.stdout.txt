

[1mTesting all IPv4 addresses (port 443): [m********** *************
--------------------------------------------------------------------------------------------------------------------------
[7m Start 2025-09-19 12:54:09                -->> **********:443 (takarekbank.hu) <<--[m

 Further IP addresses:   ************* 
 rDNS (**********):      a81e25c16ab8d500b.awsglobalaccelerator.com.
 Service detected:       HTTP


[1m[4m Testing protocols [m[4mvia sockets except NPN+ALPN [m

[1m SSLv2      [m[1;32mnot offered (OK)[m
[1m SSLv3      [m[1;32mnot offered (OK)[m
[1m TLS 1      [mnot offered
[1m TLS 1.1    [mnot offered
[1m TLS 1.2    [m[1;32moffered (OK)[m
[1m TLS 1.3    [mnot offered and downgraded to a weaker protocol
[1m NPN/SPDY   [mh2, http/1.1 (advertised)
[1m ALPN/HTTP2 [m[0;32mh2[m, http/1.1 (offered)

[1m[4m Testing cipher categories [m

[1m NULL ciphers (no encryption)                      [m[1;32mnot offered (OK)[m
[1m Anonymous NULL Ciphers (no authentication)        [m[1;32mnot offered (OK)[m
[1m Export ciphers (w/o ADH+NULL)                     [m[1;32mnot offered (OK)[m
[1m LOW: 64 Bit + DES, RC[2,4], MD5 (w/o export)      [m[0;32mnot offered (OK)[m
[1m Triple DES Ciphers / IDEA                         [mnot offered
[1m Obsoleted CBC ciphers (AES, ARIA etc.)            [mnot offered
[1m Strong encryption (AEAD ciphers) with no FS       [mnot offered
[1m Forward Secrecy strong encryption (AEAD ciphers)  [m[1;32moffered (OK)[m


[1m[4m Testing server's cipher preferences [m

Hexcode  Cipher Suite Name (OpenSSL)       KeyExch.   Encryption  Bits     Cipher Suite Name (IANA/RFC)
-----------------------------------------------------------------------------------------------------------------------------
[4mSSLv2[m
 - 
[4mSSLv3[m
 - 
[4mTLSv1[m
 - 
[4mTLSv1.1[m
 - 
[4mTLSv1.2[m (server order)
 xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH[0;32m 256[m   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256            
 xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH[0;32m 256[m   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384            
[4mTLSv1.3[m
 - 

[1m Has server cipher order?     [m[1;32myes (OK)[m


[1m[4m Testing robust forward secrecy (FS)[m[4m -- omitting Null Authentication/Encryption, 3DES, RC4 [m

[0;32m FS is offered (OK) [m          ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256 
[1m Elliptic curves offered:     [m[0;32mprime256v1[m [0;32msecp384r1[m [0;32msecp521r1[m 
[1m TLS 1.2 sig_algs offered:    [mECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 [1;33mECDSA+SHA1[m 

[1m[4m Testing server defaults (Server Hello) [m

[1m TLS extensions (standard)    [m"server name/#0" "EC point formats/#11" "renegotiation info/#65281" "session ticket/#35" "next protocol/#13172"
                              "application layer protocol negotiation/#16" "extended master secret/#23"
[1m Session Ticket RFC 5077 hint [m86400 seconds, session tickets keys seems to be rotated < daily
[1m SSL Session ID support       [myes
[1m Session Resumption           [mTickets no, ID: yes
[1m TLS clock skew[m               Random values, no fingerprinting possible 
[1m Client Authentication        [mnone
[1m Signature Algorithm          [m[0;32mECDSA with SHA256[m
[1m Server key size              [mEC [0;32m256[m bits (curve P-256)
[1m Server key usage             [mDigital Signature, Key Agreement
[1m Server extended key usage    [mTLS Web Client Authentication, TLS Web Server Authentication
[1m Serial                       [m0588420F6A087A512BE3BB4BF10A (OK: length 14)
[1m Fingerprints                 [mSHA1 EE1B00E3DBFC6295DB87703885AA89A328EF9206
                              SHA256 B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845
[1m Common Name (CN)             [m[3mmbhbank.hu [m
[1m subjectAltName (SAN)         [m[3mmbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu [m
[1m Trust (hostname)             [m[0;32mOk via SAN[m (same w/o SNI)
[1m Chain of trust[m               [1;31mNOT ok[m[1;31m:[m[0;31m Java [m(self signed CA in chain)
                              [0;32mOK: Mozilla Microsoft Linux Apple [m
[1m EV cert[m (experimental)       no 
[1m Certificate Validity (UTC)   [m[0;32m289 >= 60 days[m (2025-06-04 12:40 --> 2026-07-05 12:40)
[1m ETS/"eTLS"[m, visibility info  not present
[1m Certificate Revocation List  [mhttp://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl
[1m OCSP URI                     [mhttp://ec3sslca2017-ocsp1.e-szigno.hu
                              http://ec3sslca2017-ocsp2.e-szigno.hu
                              http://ec3sslca2017-ocsp3.e-szigno.hu
[1m OCSP stapling                [m[1;33mnot offered[m
[1m OCSP must staple extension   [m--
[1m DNS CAA RR[m (experimental)    [1;33mnot offered[m
[1m Certificate Transparency     [m[0;32myes[m (certificate extension)
[1m Certificates provided[m        4[1;33m (certificate list ordering problem)[m
[1m Issuer                       [m[3me-Szigno Class3 SSL CA 2017[m ([3mMicrosec Ltd.[m from [3mHU[m)
[1m Intermediate cert validity   [m#1: [0;32mok > 40 days[m (2026-07-05 12:40). [3mmbhbank.hu[m <-- [3me-Szigno Class3 SSL CA 2017[m
                              #2: [0;32mok > 40 days[m (2025-12-30 17:00). [3me-Szigno Class3 SSL CA 2017[m <-- [<EMAIL>[m
                              #3: [0;32mok > 40 days[m (2029-12-30 11:30). [<EMAIL>[m <-- [<EMAIL>[m
[1m Intermediate Bad OCSP[m (exp.) [0;32mOk[m


[1m[4m Testing HTTP header response @ "/" [m

[1m HTTP Status Code           [m  301 Moved Permanently, redirecting to "https://www.takarekbank.hu:443/"
[1m HTTP clock skew              [m0 sec from localtime
[1m Strict Transport Security    [m[1;33mnot offered[m
[1m Public Key Pinning           [m--
[1m Server banner                [mawselb/[33m2(B[m.[33m0(B[m
[1m Application banner           [m--
[1m Cookie(s)                    [m(none issued at "/") -- maybe better try target URL of 30x
[1m Security headers             [m[0;33m--[m
[1m Reverse Proxy banner         [m--


[1m[4m Testing vulnerabilities [m

[1m Heartbleed[m (CVE-2014-0160)                [1;32mnot vulnerable (OK)[m, no heartbeat extension
[1m CCS[m (CVE-2014-0224)                       [1;32mnot vulnerable (OK)[m
[1m Ticketbleed[m (CVE-2016-9244), experiment.  [1;32mnot vulnerable (OK)[m, reply empty
[1m ROBOT                                     [m[1;32mServer does not support any cipher suites that use RSA key transport[m
[1m Secure Renegotiation (RFC 5746)           [m[1;32msupported (OK)[m
[1m Secure Client-Initiated Renegotiation     [m[0;32mnot vulnerable (OK)[m
[1m CRIME, TLS [m(CVE-2012-4929)                [0;32mnot vulnerable (OK)[m
[1m BREACH[m (CVE-2013-3587)                    [0;32mno gzip/deflate/compress/br HTTP compression (OK) [m - only supplied "/" tested
[1m POODLE, SSL[m (CVE-2014-3566)               [1;32mnot vulnerable (OK)[m, no SSLv3 support
[1m TLS_FALLBACK_SCSV[m (RFC 7507)              [0;32mNo fallback possible (OK)[m, no protocol below TLS 1.2 offered
[1m SWEET32[m (CVE-2016-2183, CVE-2016-6329)    [1;32mnot vulnerable (OK)[m
[1m FREAK[m (CVE-2015-0204)                     [1;32mnot vulnerable (OK)[m
[1m DROWN[m (CVE-2016-0800, CVE-2016-0703)      [1;32mnot vulnerable on this host and port (OK)[m
                                           no RSA certificate, thus certificate can't be used with SSLv2 elsewhere
[1m LOGJAM[m (CVE-2015-4000), experimental      [0;32mnot vulnerable (OK):[m no DH EXPORT ciphers, no DH key detected with <= TLS 1.2
[1m BEAST[m (CVE-2011-3389)                     [0;32mnot vulnerable (OK)[m, no SSL3 or TLS1
[1m LUCKY13[m (CVE-2013-0169), experimental     [1;32mnot vulnerable (OK)[m
[1m Winshock[m (CVE-2014-6321), experimental    [1;32mnot vulnerable (OK)[m - doesn't seem to be IIS 8.x
[1m RC4[m (CVE-2013-2566, CVE-2015-2808)        [0;32mno RC4 ciphers detected (OK)[m


[1m[4m Running client simulations [m[1m[4m(HTTP) [m[1m[4mvia sockets [m

 Browser                      Protocol  Cipher Suite Name (OpenSSL)       Forward Secrecy
------------------------------------------------------------------------------------------------
 Android 6.0                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 7.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 8.1 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 9.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 10.0 (native)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 11 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 12 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Chrome 79 (Win 10)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Chrome 101 (Win 10)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Firefox 66 (Win 8.1/10)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Firefox 100 (Win 10)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 6 XP                      No connection
 IE 8 Win 7                   No connection
 IE 8 XP                      No connection
 IE 11 Win 7                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win 8.1                TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win Phone 8.1          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win 10                 TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Edge 15 Win 10               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Edge 101 Win 10 21H2         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 12.1 (iOS 12.2)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 13.0 (macOS 10.14.6)  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 15.4 (macOS 12.3.1)   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 7u25                    No connection
 Java 8u161                   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 11.0.2 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 17.0.3 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 go 1.17.8                    TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 LibreSSL 2.8.3 (Apple)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.0.2e               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.1.0l (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.1.1d (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 3.0.3 (git)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Apple Mail (16.0)            TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Thunderbird (91.9)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m


[1m[4m Rating (experimental) [m

[1m Rating specs[m (not complete)  SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)
[1m Specification documentation  [mhttps://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide
[1m Protocol Support [m(weighted)  100 (30)
[1m Key Exchange [m    (weighted)  100 (30)
[1m Cipher Strength [m (weighted)  90 (36)
[1m Final Score                  [m96
[1m Overall Grade                [m[1;32mA[m
[1m Grade cap reasons            [mGrade capped to A. HSTS is not offered

[7m Done 2025-09-19 12:54:57 [  50s] -->> **********:443 (takarekbank.hu) <<--[m

--------------------------------------------------------------------------------------------------------------------------
[7m Start 2025-09-19 12:54:57                -->> *************:443 (takarekbank.hu) <<--[m

 Further IP addresses:   ********** 
 rDNS (*************):   a81e25c16ab8d500b.awsglobalaccelerator.com.
 Service detected:       HTTP


[1m[4m Testing protocols [m[4mvia sockets except NPN+ALPN [m

[1m SSLv2      [m[1;32mnot offered (OK)[m
[1m SSLv3      [m[1;32mnot offered (OK)[m
[1m TLS 1      [mnot offered
[1m TLS 1.1    [mnot offered
[1m TLS 1.2    [m[1;32moffered (OK)[m
[1m TLS 1.3    [mnot offered and downgraded to a weaker protocol
[1m NPN/SPDY   [mh2, http/1.1 (advertised)
[1m ALPN/HTTP2 [m[0;32mh2[m, http/1.1 (offered)

[1m[4m Testing cipher categories [m

[1m NULL ciphers (no encryption)                      [m[1;32mnot offered (OK)[m
[1m Anonymous NULL Ciphers (no authentication)        [m[1;32mnot offered (OK)[m
[1m Export ciphers (w/o ADH+NULL)                     [m[1;32mnot offered (OK)[m
[1m LOW: 64 Bit + DES, RC[2,4], MD5 (w/o export)      [m[0;32mnot offered (OK)[m
[1m Triple DES Ciphers / IDEA                         [mnot offered
[1m Obsoleted CBC ciphers (AES, ARIA etc.)            [mnot offered
[1m Strong encryption (AEAD ciphers) with no FS       [mnot offered
[1m Forward Secrecy strong encryption (AEAD ciphers)  [m[1;32moffered (OK)[m


[1m[4m Testing server's cipher preferences [m

Hexcode  Cipher Suite Name (OpenSSL)       KeyExch.   Encryption  Bits     Cipher Suite Name (IANA/RFC)
-----------------------------------------------------------------------------------------------------------------------------
[4mSSLv2[m
 - 
[4mSSLv3[m
 - 
[4mTLSv1[m
 - 
[4mTLSv1.1[m
 - 
[4mTLSv1.2[m (server order)
 xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH[0;32m 256[m   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256            
 xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH[0;32m 256[m   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384            
[4mTLSv1.3[m
 - 

[1m Has server cipher order?     [m[1;32myes (OK)[m


[1m[4m Testing robust forward secrecy (FS)[m[4m -- omitting Null Authentication/Encryption, 3DES, RC4 [m

[0;32m FS is offered (OK) [m          ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256 
[1m Elliptic curves offered:     [m[0;32mprime256v1[m [0;32msecp384r1[m [0;32msecp521r1[m 
[1m TLS 1.2 sig_algs offered:    [mECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 [1;33mECDSA+SHA1[m 

[1m[4m Testing server defaults (Server Hello) [m

[1m TLS extensions (standard)    [m"server name/#0" "EC point formats/#11" "renegotiation info/#65281" "session ticket/#35" "next protocol/#13172"
                              "application layer protocol negotiation/#16" "extended master secret/#23"
[1m Session Ticket RFC 5077 hint [m86400 seconds, session tickets keys seems to be rotated < daily
[1m SSL Session ID support       [myes
[1m Session Resumption           [mTickets: yes, ID: no
[1m TLS clock skew[m               Random values, no fingerprinting possible 
[1m Client Authentication        [mnone
[1m Signature Algorithm          [m[0;32mECDSA with SHA256[m
[1m Server key size              [mEC [0;32m256[m bits (curve P-256)
[1m Server key usage             [mDigital Signature, Key Agreement
[1m Server extended key usage    [mTLS Web Client Authentication, TLS Web Server Authentication
[1m Serial                       [m0588420F6A087A512BE3BB4BF10A (OK: length 14)
[1m Fingerprints                 [mSHA1 EE1B00E3DBFC6295DB87703885AA89A328EF9206
                              SHA256 B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845
[1m Common Name (CN)             [m[3mmbhbank.hu [m
[1m subjectAltName (SAN)         [m[3mmbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu [m
[1m Trust (hostname)             [m[0;32mOk via SAN[m (same w/o SNI)
[1m Chain of trust[m               [1;31mNOT ok[m[1;31m:[m[0;31m Java [m(self signed CA in chain)
                              [0;32mOK: Mozilla Microsoft Linux Apple [m
[1m EV cert[m (experimental)       no 
[1m Certificate Validity (UTC)   [m[0;32m289 >= 60 days[m (2025-06-04 12:40 --> 2026-07-05 12:40)
[1m ETS/"eTLS"[m, visibility info  not present
[1m Certificate Revocation List  [mhttp://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl
[1m OCSP URI                     [mhttp://ec3sslca2017-ocsp1.e-szigno.hu
                              http://ec3sslca2017-ocsp2.e-szigno.hu
                              http://ec3sslca2017-ocsp3.e-szigno.hu
[1m OCSP stapling                [m[1;33mnot offered[m
[1m OCSP must staple extension   [m--
[1m DNS CAA RR[m (experimental)    [1;33mnot offered[m
[1m Certificate Transparency     [m[0;32myes[m (certificate extension)
[1m Certificates provided[m        4[1;33m (certificate list ordering problem)[m
[1m Issuer                       [m[3me-Szigno Class3 SSL CA 2017[m ([3mMicrosec Ltd.[m from [3mHU[m)
[1m Intermediate cert validity   [m#1: [0;32mok > 40 days[m (2026-07-05 12:40). [3mmbhbank.hu[m <-- [3me-Szigno Class3 SSL CA 2017[m
                              #2: [0;32mok > 40 days[m (2025-12-30 17:00). [3me-Szigno Class3 SSL CA 2017[m <-- [<EMAIL>[m
                              #3: [0;32mok > 40 days[m (2029-12-30 11:30). [<EMAIL>[m <-- [<EMAIL>[m
[1m Intermediate Bad OCSP[m (exp.) [0;32mOk[m


[1m[4m Testing HTTP header response @ "/" [m

[1m HTTP Status Code           [m  301 Moved Permanently, redirecting to "https://www.takarekbank.hu:443/"
[1m HTTP clock skew              [m0 sec from localtime
[1m Strict Transport Security    [m[1;33mnot offered[m
[1m Public Key Pinning           [m--
[1m Server banner                [mawselb/[33m2(B[m.[33m0(B[m
[1m Application banner           [m--
[1m Cookie(s)                    [m(none issued at "/") -- maybe better try target URL of 30x
[1m Security headers             [m[0;33m--[m
[1m Reverse Proxy banner         [m--


[1m[4m Testing vulnerabilities [m

[1m Heartbleed[m (CVE-2014-0160)                [1;32mnot vulnerable (OK)[m, no heartbeat extension
[1m CCS[m (CVE-2014-0224)                       [1;32mnot vulnerable (OK)[m
[1m Ticketbleed[m (CVE-2016-9244), experiment.  [1;32mnot vulnerable (OK)[m, reply empty
[1m ROBOT                                     [m[1;32mServer does not support any cipher suites that use RSA key transport[m
[1m Secure Renegotiation (RFC 5746)           [m[1;32msupported (OK)[m
[1m Secure Client-Initiated Renegotiation     [m[0;32mnot vulnerable (OK)[m
[1m CRIME, TLS [m(CVE-2012-4929)                [0;32mnot vulnerable (OK)[m
[1m BREACH[m (CVE-2013-3587)                    [0;32mno gzip/deflate/compress/br HTTP compression (OK) [m - only supplied "/" tested
[1m POODLE, SSL[m (CVE-2014-3566)               [1;32mnot vulnerable (OK)[m, no SSLv3 support
[1m TLS_FALLBACK_SCSV[m (RFC 7507)              [0;32mNo fallback possible (OK)[m, no protocol below TLS 1.2 offered
[1m SWEET32[m (CVE-2016-2183, CVE-2016-6329)    [1;32mnot vulnerable (OK)[m
[1m FREAK[m (CVE-2015-0204)                     [1;32mnot vulnerable (OK)[m
[1m DROWN[m (CVE-2016-0800, CVE-2016-0703)      [1;32mnot vulnerable on this host and port (OK)[m
                                           no RSA certificate, thus certificate can't be used with SSLv2 elsewhere
[1m LOGJAM[m (CVE-2015-4000), experimental      [0;32mnot vulnerable (OK):[m no DH EXPORT ciphers, no DH key detected with <= TLS 1.2
[1m BEAST[m (CVE-2011-3389)                     [0;32mnot vulnerable (OK)[m, no SSL3 or TLS1
[1m LUCKY13[m (CVE-2013-0169), experimental     [1;32mnot vulnerable (OK)[m
[1m Winshock[m (CVE-2014-6321), experimental    [1;32mnot vulnerable (OK)[m - doesn't seem to be IIS 8.x
[1m RC4[m (CVE-2013-2566, CVE-2015-2808)        [0;32mno RC4 ciphers detected (OK)[m


[1m[4m Running client simulations [m[1m[4m(HTTP) [m[1m[4mvia sockets [m

 Browser                      Protocol  Cipher Suite Name (OpenSSL)       Forward Secrecy
------------------------------------------------------------------------------------------------
 Android 6.0                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 7.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 8.1 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 9.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 10.0 (native)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 11 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Android 12 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Chrome 79 (Win 10)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Chrome 101 (Win 10)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Firefox 66 (Win 8.1/10)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Firefox 100 (Win 10)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 6 XP                      No connection
 IE 8 Win 7                   No connection
 IE 8 XP                      No connection
 IE 11 Win 7                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win 8.1                TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win Phone 8.1          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 IE 11 Win 10                 TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Edge 15 Win 10               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Edge 101 Win 10 21H2         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 12.1 (iOS 12.2)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 13.0 (macOS 10.14.6)  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Safari 15.4 (macOS 12.3.1)   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 7u25                    No connection
 Java 8u161                   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 11.0.2 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Java 17.0.3 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 go 1.17.8                    TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 LibreSSL 2.8.3 (Apple)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.0.2e               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.1.0l (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 1.1.1d (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 OpenSSL 3.0.3 (git)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Apple Mail (16.0)            TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m
 Thunderbird (91.9)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     [0;32m256 bit ECDH (P-256)[m


[1m[4m Rating (experimental) [m

[1m Rating specs[m (not complete)  SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)
[1m Specification documentation  [mhttps://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide
[1m Protocol Support [m(weighted)  100 (30)
[1m Key Exchange [m    (weighted)  100 (30)
[1m Cipher Strength [m (weighted)  90 (36)
[1m Final Score                  [m96
[1m Overall Grade                [m[1;32mA[m
[1m Grade cap reasons            [mGrade capped to A. HSTS is not offered

[7m Done 2025-09-19 12:55:49 [ 102s] -->> *************:443 (takarekbank.hu) <<--[m

--------------------------------------------------------------------------------------------------------------------------
[1mDone testing now all IP addresses (on port 443): [m********** *************

