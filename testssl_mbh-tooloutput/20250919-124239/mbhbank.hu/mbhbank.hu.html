<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<!-- This file was created with testssl.sh. https://testssl.sh -->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="application/xhtml+xml; charset=UTF-8" />
<title>testssl.sh</title>
</head>
<body>
<pre>


<span style="font-weight:bold;">Testing all IPv4 addresses (port 443): </span>********** *************
--------------------------------------------------------------------------------------------------------------------------
<span style="color:white;background-color:black;"> Start 2025-09-19 12:42:41                --&gt;&gt; **********:443 (mbhbank.hu) &lt;&lt;--</span>

 Further IP addresses:   ************* 
 rDNS (**********):      a81e25c16ab8d500b.awsglobalaccelerator.com.
 Service detected:       HTTP


<span style="text-decoration:underline;font-weight:bold;"> Testing protocols </span><u>via sockets except NPN+ALPN </u>

<span style="font-weight:bold;"> SSLv2      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> SSLv3      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> TLS 1      </span>not offered
<span style="font-weight:bold;"> TLS 1.1    </span>not offered
<span style="font-weight:bold;"> TLS 1.2    </span><span style="color:#008817;font-weight:bold;">offered (OK)</span>
<span style="font-weight:bold;"> TLS 1.3    </span>not offered and downgraded to a weaker protocol
<span style="font-weight:bold;"> NPN/SPDY   </span>h2, http/1.1 (advertised)
<span style="font-weight:bold;"> ALPN/HTTP2 </span><span style="color:#008817;">h2</span>, http/1.1 (offered)

<span style="text-decoration:underline;font-weight:bold;"> Testing cipher categories </span>

<span style="font-weight:bold;"> NULL ciphers (no encryption)                      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> Anonymous NULL Ciphers (no authentication)        </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> Export ciphers (w/o ADH+NULL)                     </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> LOW: 64 Bit + DES, RC[2,4], MD5 (w/o export)      </span><span style="color:#008817;">not offered (OK)</span>
<span style="font-weight:bold;"> Triple DES Ciphers / IDEA                         </span>not offered
<span style="font-weight:bold;"> Obsoleted CBC ciphers (AES, ARIA etc.)            </span>not offered
<span style="font-weight:bold;"> Strong encryption (AEAD ciphers) with no FS       </span>not offered
<span style="font-weight:bold;"> Forward Secrecy strong encryption (AEAD ciphers)  </span><span style="color:#008817;font-weight:bold;">offered (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing server&apos;s cipher preferences </span>

Hexcode  Cipher Suite Name (OpenSSL)       KeyExch.   Encryption  Bits     Cipher Suite Name (IANA/RFC)
-----------------------------------------------------------------------------------------------------------------------------
<u>SSLv2</u>
 - 
<u>SSLv3</u>
 - 
<u>TLSv1</u>
 - 
<u>TLSv1.1</u>
 - 
<u>TLSv1.2</u> (server order)
 xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH<span style="color:#008817;"> 256</span>   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256            
 xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH<span style="color:#008817;"> 256</span>   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384            
<u>TLSv1.3</u>
 - 

<span style="font-weight:bold;"> Has server cipher order?     </span><span style="color:#008817;font-weight:bold;">yes (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing robust forward secrecy (FS)</span><u> -- omitting Null Authentication/Encryption, 3DES, RC4 </u>

<span style="color:#008817;"> FS is offered (OK) </span>          ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256 
<span style="font-weight:bold;"> Elliptic curves offered:     </span><span style="color:#008817;">prime256v1</span> <span style="color:#008817;">secp384r1</span> <span style="color:#008817;">secp521r1</span> 
<span style="font-weight:bold;"> TLS 1.2 sig_algs offered:    </span>ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 <span style="color:#a86437;">ECDSA+SHA1</span> 

<span style="text-decoration:underline;font-weight:bold;"> Testing server defaults (Server Hello) </span>

<span style="font-weight:bold;"> TLS extensions (standard)    </span>&quot;server name/#0&quot; &quot;EC point formats/#11&quot; &quot;renegotiation info/#65281&quot; &quot;session ticket/#35&quot; &quot;next protocol/#13172&quot;
                              &quot;application layer protocol negotiation/#16&quot; &quot;extended master secret/#23&quot;
<span style="font-weight:bold;"> Session Ticket RFC 5077 hint </span>86400 seconds, session tickets keys seems to be rotated &lt; daily
<span style="font-weight:bold;"> SSL Session ID support       </span>yes
<span style="font-weight:bold;"> Session Resumption           </span>Tickets no, ID: yes
<span style="font-weight:bold;"> TLS clock skew</span>               Random values, no fingerprinting possible 
<span style="font-weight:bold;"> Client Authentication        </span>none
<span style="font-weight:bold;"> Signature Algorithm          </span><span style="color:#008817;">ECDSA with SHA256</span>
<span style="font-weight:bold;"> Server key size              </span>EC <span style="color:#008817;">256</span> bits (curve P-256)
<span style="font-weight:bold;"> Server key usage             </span>Digital Signature, Key Agreement
<span style="font-weight:bold;"> Server extended key usage    </span>TLS Web Client Authentication, TLS Web Server Authentication
<span style="font-weight:bold;"> Serial                       </span>0588420F6A087A512BE3BB4BF10A (OK: length 14)
<span style="font-weight:bold;"> Fingerprints                 </span>SHA1 EE1B00E3DBFC6295DB87703885AA89A328EF9206
                              SHA256 B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845
<span style="font-weight:bold;"> Common Name (CN)             </span><i>mbhbank.hu </i>
<span style="font-weight:bold;"> subjectAltName (SAN)         </span><i>mbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu </i>
<span style="font-weight:bold;"> Trust (hostname)             </span><span style="color:#008817;">Ok via SAN and CN</span> (same w/o SNI)
<span style="font-weight:bold;"> Chain of trust</span>               <span style="color:#e52207;font-weight:bold;">NOT ok</span><span style="color:#e52207;font-weight:bold;">:</span><span style="color:#e52207;"> Java </span>(self signed CA in chain)
<span style="color:#008817;">OK: Mozilla Microsoft Linux Apple </span>
<span style="font-weight:bold;"> EV cert</span> (experimental)       no 
<span style="font-weight:bold;"> Certificate Validity (UTC)   </span><span style="color:#008817;">289 &gt;= 60 days</span> (2025-06-04 12:40 --&gt; 2026-07-05 12:40)
<span style="font-weight:bold;"> ETS/&quot;eTLS&quot;</span>, visibility info  not present
<span style="font-weight:bold;"> Certificate Revocation List  </span>http://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl
<span style="font-weight:bold;"> OCSP URI                     </span>http://ec3sslca2017-ocsp1.e-szigno.hu
                              http://ec3sslca2017-ocsp2.e-szigno.hu
                              http://ec3sslca2017-ocsp3.e-szigno.hu
<span style="font-weight:bold;"> OCSP stapling                </span><span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> OCSP must staple extension   </span>--
<span style="font-weight:bold;"> DNS CAA RR</span> (experimental)    <span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> Certificate Transparency     </span><span style="color:#008817;">yes</span> (certificate extension)
<span style="font-weight:bold;"> Certificates provided</span>        4<span style="color:#a86437;"> (certificate list ordering problem)</span>
<span style="font-weight:bold;"> Issuer                       </span><i>e-Szigno Class3 SSL CA 2017</i> (<i>Microsec Ltd.</i> from <i>HU</i>)
<span style="font-weight:bold;"> Intermediate cert validity   </span>#1: <span style="color:#008817;">ok &gt; 40 days</span> (2026-07-05 12:40). <i>mbhbank.hu</i> &lt;-- <i>e-Szigno Class3 SSL CA 2017</i>
                              #2: <span style="color:#008817;">ok &gt; 40 days</span> (2025-12-30 17:00). <i>e-Szigno Class3 SSL CA 2017</i> &lt;-- <i><EMAIL></i>
                              #3: <span style="color:#008817;">ok &gt; 40 days</span> (2029-12-30 11:30). <i><EMAIL></i> &lt;-- <i><EMAIL></i>
<span style="font-weight:bold;"> Intermediate Bad OCSP</span> (exp.) <span style="color:#008817;">Ok</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing HTTP header response @ &quot;/&quot; </span>

<span style="font-weight:bold;"> HTTP Status Code           </span>  301 Moved Permanently, redirecting to &quot;<a href="https://www.mbhbank.hu:443/" style="color:black;text-decoration:none;">https://www.mbhbank.hu:443/</a>&quot;
<span style="font-weight:bold;"> HTTP clock skew              </span>0 sec from localtime
<span style="font-weight:bold;"> Strict Transport Security    </span><span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> Public Key Pinning           </span>--
<span style="font-weight:bold;"> Server banner                </span>awselb/<span style="color:#8a7237;">2</span>.<span style="color:#8a7237;">0</span>
<span style="font-weight:bold;"> Application banner           </span>--
<span style="font-weight:bold;"> Cookie(s)                    </span>(none issued at &quot;/&quot;) -- maybe better try target URL of 30x
<span style="font-weight:bold;"> Security headers             </span><span style="color:#c05600;">--</span>
<span style="font-weight:bold;"> Reverse Proxy banner         </span>--


<span style="text-decoration:underline;font-weight:bold;"> Testing vulnerabilities </span>

<span style="font-weight:bold;"> Heartbleed</span> (CVE-2014-0160)                <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, no heartbeat extension
<span style="font-weight:bold;"> CCS</span> (CVE-2014-0224)                       <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> Ticketbleed</span> (CVE-2016-9244), experiment.  <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, reply empty
<span style="font-weight:bold;"> ROBOT                                     </span><span style="color:#008817;font-weight:bold;">Server does not support any cipher suites that use RSA key transport</span>
<span style="font-weight:bold;"> Secure Renegotiation (RFC 5746)           </span><span style="color:#008817;font-weight:bold;">supported (OK)</span>
<span style="font-weight:bold;"> Secure Client-Initiated Renegotiation     </span><span style="color:#008817;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> CRIME, TLS </span>(CVE-2012-4929)                <span style="color:#008817;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> BREACH</span> (CVE-2013-3587)                    <span style="color:#008817;">no gzip/deflate/compress/br HTTP compression (OK) </span> - only supplied &quot;/&quot; tested
<span style="font-weight:bold;"> POODLE, SSL</span> (CVE-2014-3566)               <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, no SSLv3 support
<span style="font-weight:bold;"> TLS_FALLBACK_SCSV</span> (RFC 7507)              <span style="color:#008817;">No fallback possible (OK)</span>, no protocol below TLS 1.2 offered
<span style="font-weight:bold;"> SWEET32</span> (CVE-2016-2183, CVE-2016-6329)    <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> FREAK</span> (CVE-2015-0204)                     <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> DROWN</span> (CVE-2016-0800, CVE-2016-0703)      <span style="color:#008817;font-weight:bold;">not vulnerable on this host and port (OK)</span>
                                           no RSA certificate, thus certificate can&apos;t be used with SSLv2 elsewhere
<span style="font-weight:bold;"> LOGJAM</span> (CVE-2015-4000), experimental      <span style="color:#008817;">not vulnerable (OK):</span> no DH EXPORT ciphers, no DH key detected with &lt;= TLS 1.2
<span style="font-weight:bold;"> BEAST</span> (CVE-2011-3389)                     <span style="color:#008817;">not vulnerable (OK)</span>, no SSL3 or TLS1
<span style="font-weight:bold;"> LUCKY13</span> (CVE-2013-0169), experimental     <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> Winshock</span> (CVE-2014-6321), experimental    <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span> - doesn&apos;t seem to be IIS 8.x
<span style="font-weight:bold;"> RC4</span> (CVE-2013-2566, CVE-2015-2808)        <span style="color:#008817;">no RC4 ciphers detected (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Running client simulations </span><span style="text-decoration:underline;font-weight:bold;">(HTTP) </span><span style="text-decoration:underline;font-weight:bold;">via sockets </span>

 Browser                      Protocol  Cipher Suite Name (OpenSSL)       Forward Secrecy
------------------------------------------------------------------------------------------------
 Android 6.0                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 7.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 8.1 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 9.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 10.0 (native)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 11 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 12 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Chrome 79 (Win 10)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Chrome 101 (Win 10)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Firefox 66 (Win 8.1/10)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Firefox 100 (Win 10)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 6 XP                      No connection
 IE 8 Win 7                   No connection
 IE 8 XP                      No connection
 IE 11 Win 7                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win 8.1                TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win Phone 8.1          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win 10                 TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Edge 15 Win 10               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Edge 101 Win 10 21H2         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 12.1 (iOS 12.2)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 13.0 (macOS 10.14.6)  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 15.4 (macOS 12.3.1)   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 7u25                    No connection
 Java 8u161                   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 11.0.2 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 17.0.3 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 go 1.17.8                    TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 LibreSSL 2.8.3 (Apple)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.0.2e               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.1.0l (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.1.1d (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 3.0.3 (git)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Apple Mail (16.0)            TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Thunderbird (91.9)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>


<span style="text-decoration:underline;font-weight:bold;"> Rating (experimental) </span>

<span style="font-weight:bold;"> Rating specs</span> (not complete)  SSL Labs&apos;s &apos;SSL Server Rating Guide&apos; (version 2009q from 2020-01-30)
<span style="font-weight:bold;"> Specification documentation  </span><a href="https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide" style="color:black;text-decoration:none;">https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide</a>
<span style="font-weight:bold;"> Protocol Support </span>(weighted)  100 (30)
<span style="font-weight:bold;"> Key Exchange </span>    (weighted)  100 (30)
<span style="font-weight:bold;"> Cipher Strength </span> (weighted)  90 (36)
<span style="font-weight:bold;"> Final Score                  </span>96
<span style="font-weight:bold;"> Overall Grade                </span><span style="color:#008817;font-weight:bold;">A</span>
<span style="font-weight:bold;"> Grade cap reasons            </span>Grade capped to A. HSTS is not offered

<span style="color:white;background-color:black;"> Done 2025-09-19 12:43:28 [  49s] --&gt;&gt; **********:443 (mbhbank.hu) &lt;&lt;--</span>

--------------------------------------------------------------------------------------------------------------------------
<span style="color:white;background-color:black;"> Start 2025-09-19 12:43:28                --&gt;&gt; *************:443 (mbhbank.hu) &lt;&lt;--</span>

 Further IP addresses:   ********** 
 rDNS (*************):   a81e25c16ab8d500b.awsglobalaccelerator.com.
 Service detected:       HTTP


<span style="text-decoration:underline;font-weight:bold;"> Testing protocols </span><u>via sockets except NPN+ALPN </u>

<span style="font-weight:bold;"> SSLv2      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> SSLv3      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> TLS 1      </span>not offered
<span style="font-weight:bold;"> TLS 1.1    </span>not offered
<span style="font-weight:bold;"> TLS 1.2    </span><span style="color:#008817;font-weight:bold;">offered (OK)</span>
<span style="font-weight:bold;"> TLS 1.3    </span>not offered and downgraded to a weaker protocol
<span style="font-weight:bold;"> NPN/SPDY   </span>h2, http/1.1 (advertised)
<span style="font-weight:bold;"> ALPN/HTTP2 </span><span style="color:#008817;">h2</span>, http/1.1 (offered)

<span style="text-decoration:underline;font-weight:bold;"> Testing cipher categories </span>

<span style="font-weight:bold;"> NULL ciphers (no encryption)                      </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> Anonymous NULL Ciphers (no authentication)        </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> Export ciphers (w/o ADH+NULL)                     </span><span style="color:#008817;font-weight:bold;">not offered (OK)</span>
<span style="font-weight:bold;"> LOW: 64 Bit + DES, RC[2,4], MD5 (w/o export)      </span><span style="color:#008817;">not offered (OK)</span>
<span style="font-weight:bold;"> Triple DES Ciphers / IDEA                         </span>not offered
<span style="font-weight:bold;"> Obsoleted CBC ciphers (AES, ARIA etc.)            </span>not offered
<span style="font-weight:bold;"> Strong encryption (AEAD ciphers) with no FS       </span>not offered
<span style="font-weight:bold;"> Forward Secrecy strong encryption (AEAD ciphers)  </span><span style="color:#008817;font-weight:bold;">offered (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing server&apos;s cipher preferences </span>

Hexcode  Cipher Suite Name (OpenSSL)       KeyExch.   Encryption  Bits     Cipher Suite Name (IANA/RFC)
-----------------------------------------------------------------------------------------------------------------------------
<u>SSLv2</u>
 - 
<u>SSLv3</u>
 - 
<u>TLSv1</u>
 - 
<u>TLSv1.1</u>
 - 
<u>TLSv1.2</u> (server order)
 xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH<span style="color:#008817;"> 256</span>   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256            
 xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH<span style="color:#008817;"> 256</span>   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384            
<u>TLSv1.3</u>
 - 

<span style="font-weight:bold;"> Has server cipher order?     </span><span style="color:#008817;font-weight:bold;">yes (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing robust forward secrecy (FS)</span><u> -- omitting Null Authentication/Encryption, 3DES, RC4 </u>

<span style="color:#008817;"> FS is offered (OK) </span>          ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256 
<span style="font-weight:bold;"> Elliptic curves offered:     </span><span style="color:#008817;">prime256v1</span> <span style="color:#008817;">secp384r1</span> <span style="color:#008817;">secp521r1</span> 
<span style="font-weight:bold;"> TLS 1.2 sig_algs offered:    </span>ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 <span style="color:#a86437;">ECDSA+SHA1</span> 

<span style="text-decoration:underline;font-weight:bold;"> Testing server defaults (Server Hello) </span>

<span style="font-weight:bold;"> TLS extensions (standard)    </span>&quot;server name/#0&quot; &quot;EC point formats/#11&quot; &quot;renegotiation info/#65281&quot; &quot;session ticket/#35&quot; &quot;next protocol/#13172&quot;
                              &quot;application layer protocol negotiation/#16&quot; &quot;extended master secret/#23&quot;
<span style="font-weight:bold;"> Session Ticket RFC 5077 hint </span>86400 seconds, session tickets keys seems to be rotated &lt; daily
<span style="font-weight:bold;"> SSL Session ID support       </span>yes
<span style="font-weight:bold;"> Session Resumption           </span>Tickets no, ID: no
<span style="font-weight:bold;"> TLS clock skew</span>               Random values, no fingerprinting possible 
<span style="font-weight:bold;"> Client Authentication        </span>none
<span style="font-weight:bold;"> Signature Algorithm          </span><span style="color:#008817;">ECDSA with SHA256</span>
<span style="font-weight:bold;"> Server key size              </span>EC <span style="color:#008817;">256</span> bits (curve P-256)
<span style="font-weight:bold;"> Server key usage             </span>Digital Signature, Key Agreement
<span style="font-weight:bold;"> Server extended key usage    </span>TLS Web Client Authentication, TLS Web Server Authentication
<span style="font-weight:bold;"> Serial                       </span>0588420F6A087A512BE3BB4BF10A (OK: length 14)
<span style="font-weight:bold;"> Fingerprints                 </span>SHA1 EE1B00E3DBFC6295DB87703885AA89A328EF9206
                              SHA256 B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845
<span style="font-weight:bold;"> Common Name (CN)             </span><i>mbhbank.hu </i>
<span style="font-weight:bold;"> subjectAltName (SAN)         </span><i>mbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu </i>
<span style="font-weight:bold;"> Trust (hostname)             </span><span style="color:#008817;">Ok via SAN and CN</span> (same w/o SNI)
<span style="font-weight:bold;"> Chain of trust</span>               <span style="color:#e52207;font-weight:bold;">NOT ok</span><span style="color:#e52207;font-weight:bold;">:</span><span style="color:#e52207;"> Java </span>(self signed CA in chain)
<span style="color:#008817;">OK: Mozilla Microsoft Linux Apple </span>
<span style="font-weight:bold;"> EV cert</span> (experimental)       no 
<span style="font-weight:bold;"> Certificate Validity (UTC)   </span><span style="color:#008817;">289 &gt;= 60 days</span> (2025-06-04 12:40 --&gt; 2026-07-05 12:40)
<span style="font-weight:bold;"> ETS/&quot;eTLS&quot;</span>, visibility info  not present
<span style="font-weight:bold;"> Certificate Revocation List  </span>http://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl
                              http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl
<span style="font-weight:bold;"> OCSP URI                     </span>http://ec3sslca2017-ocsp1.e-szigno.hu
                              http://ec3sslca2017-ocsp2.e-szigno.hu
                              http://ec3sslca2017-ocsp3.e-szigno.hu
<span style="font-weight:bold;"> OCSP stapling                </span><span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> OCSP must staple extension   </span>--
<span style="font-weight:bold;"> DNS CAA RR</span> (experimental)    <span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> Certificate Transparency     </span><span style="color:#008817;">yes</span> (certificate extension)
<span style="font-weight:bold;"> Certificates provided</span>        4<span style="color:#a86437;"> (certificate list ordering problem)</span>
<span style="font-weight:bold;"> Issuer                       </span><i>e-Szigno Class3 SSL CA 2017</i> (<i>Microsec Ltd.</i> from <i>HU</i>)
<span style="font-weight:bold;"> Intermediate cert validity   </span>#1: <span style="color:#008817;">ok &gt; 40 days</span> (2026-07-05 12:40). <i>mbhbank.hu</i> &lt;-- <i>e-Szigno Class3 SSL CA 2017</i>
                              #2: <span style="color:#008817;">ok &gt; 40 days</span> (2025-12-30 17:00). <i>e-Szigno Class3 SSL CA 2017</i> &lt;-- <i><EMAIL></i>
                              #3: <span style="color:#008817;">ok &gt; 40 days</span> (2029-12-30 11:30). <i><EMAIL></i> &lt;-- <i><EMAIL></i>
<span style="font-weight:bold;"> Intermediate Bad OCSP</span> (exp.) <span style="color:#008817;">Ok</span>


<span style="text-decoration:underline;font-weight:bold;"> Testing HTTP header response @ &quot;/&quot; </span>

<span style="font-weight:bold;"> HTTP Status Code           </span>  301 Moved Permanently, redirecting to &quot;<a href="https://www.mbhbank.hu:443/" style="color:black;text-decoration:none;">https://www.mbhbank.hu:443/</a>&quot;
<span style="font-weight:bold;"> HTTP clock skew              </span>0 sec from localtime
<span style="font-weight:bold;"> Strict Transport Security    </span><span style="color:#a86437;">not offered</span>
<span style="font-weight:bold;"> Public Key Pinning           </span>--
<span style="font-weight:bold;"> Server banner                </span>awselb/<span style="color:#8a7237;">2</span>.<span style="color:#8a7237;">0</span>
<span style="font-weight:bold;"> Application banner           </span>--
<span style="font-weight:bold;"> Cookie(s)                    </span>(none issued at &quot;/&quot;) -- maybe better try target URL of 30x
<span style="font-weight:bold;"> Security headers             </span><span style="color:#c05600;">--</span>
<span style="font-weight:bold;"> Reverse Proxy banner         </span>--


<span style="text-decoration:underline;font-weight:bold;"> Testing vulnerabilities </span>

<span style="font-weight:bold;"> Heartbleed</span> (CVE-2014-0160)                <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, no heartbeat extension
<span style="font-weight:bold;"> CCS</span> (CVE-2014-0224)                       <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> Ticketbleed</span> (CVE-2016-9244), experiment.  <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, reply empty
<span style="font-weight:bold;"> ROBOT                                     </span><span style="color:#008817;font-weight:bold;">Server does not support any cipher suites that use RSA key transport</span>
<span style="font-weight:bold;"> Secure Renegotiation (RFC 5746)           </span><span style="color:#008817;font-weight:bold;">supported (OK)</span>
<span style="font-weight:bold;"> Secure Client-Initiated Renegotiation     </span><span style="color:#008817;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> CRIME, TLS </span>(CVE-2012-4929)                <span style="color:#008817;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> BREACH</span> (CVE-2013-3587)                    <span style="color:#008817;">no gzip/deflate/compress/br HTTP compression (OK) </span> - only supplied &quot;/&quot; tested
<span style="font-weight:bold;"> POODLE, SSL</span> (CVE-2014-3566)               <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>, no SSLv3 support
<span style="font-weight:bold;"> TLS_FALLBACK_SCSV</span> (RFC 7507)              <span style="color:#008817;">No fallback possible (OK)</span>, no protocol below TLS 1.2 offered
<span style="font-weight:bold;"> SWEET32</span> (CVE-2016-2183, CVE-2016-6329)    <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> FREAK</span> (CVE-2015-0204)                     <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> DROWN</span> (CVE-2016-0800, CVE-2016-0703)      <span style="color:#008817;font-weight:bold;">not vulnerable on this host and port (OK)</span>
                                           no RSA certificate, thus certificate can&apos;t be used with SSLv2 elsewhere
<span style="font-weight:bold;"> LOGJAM</span> (CVE-2015-4000), experimental      <span style="color:#008817;">not vulnerable (OK):</span> no DH EXPORT ciphers, no DH key detected with &lt;= TLS 1.2
<span style="font-weight:bold;"> BEAST</span> (CVE-2011-3389)                     <span style="color:#008817;">not vulnerable (OK)</span>, no SSL3 or TLS1
<span style="font-weight:bold;"> LUCKY13</span> (CVE-2013-0169), experimental     <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span>
<span style="font-weight:bold;"> Winshock</span> (CVE-2014-6321), experimental    <span style="color:#008817;font-weight:bold;">not vulnerable (OK)</span> - doesn&apos;t seem to be IIS 8.x
<span style="font-weight:bold;"> RC4</span> (CVE-2013-2566, CVE-2015-2808)        <span style="color:#008817;">no RC4 ciphers detected (OK)</span>


<span style="text-decoration:underline;font-weight:bold;"> Running client simulations </span><span style="text-decoration:underline;font-weight:bold;">(HTTP) </span><span style="text-decoration:underline;font-weight:bold;">via sockets </span>

 Browser                      Protocol  Cipher Suite Name (OpenSSL)       Forward Secrecy
------------------------------------------------------------------------------------------------
 Android 6.0                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 7.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 8.1 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 9.0 (native)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 10.0 (native)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 11 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Android 12 (native)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Chrome 79 (Win 10)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Chrome 101 (Win 10)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Firefox 66 (Win 8.1/10)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Firefox 100 (Win 10)         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 6 XP                      No connection
 IE 8 Win 7                   No connection
 IE 8 XP                      No connection
 IE 11 Win 7                  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win 8.1                TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win Phone 8.1          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 IE 11 Win 10                 TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Edge 15 Win 10               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Edge 101 Win 10 21H2         TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 12.1 (iOS 12.2)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 13.0 (macOS 10.14.6)  TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Safari 15.4 (macOS 12.3.1)   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 7u25                    No connection
 Java 8u161                   TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 11.0.2 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Java 17.0.3 (OpenJDK)        TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 go 1.17.8                    TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 LibreSSL 2.8.3 (Apple)       TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.0.2e               TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.1.0l (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 1.1.1d (Debian)      TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 OpenSSL 3.0.3 (git)          TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Apple Mail (16.0)            TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>
 Thunderbird (91.9)           TLSv1.2   ECDHE-ECDSA-AES128-GCM-SHA256     <span style="color:#008817;">256 bit ECDH (P-256)</span>


<span style="text-decoration:underline;font-weight:bold;"> Rating (experimental) </span>

<span style="font-weight:bold;"> Rating specs</span> (not complete)  SSL Labs&apos;s &apos;SSL Server Rating Guide&apos; (version 2009q from 2020-01-30)
<span style="font-weight:bold;"> Specification documentation  </span><a href="https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide" style="color:black;text-decoration:none;">https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide</a>
<span style="font-weight:bold;"> Protocol Support </span>(weighted)  100 (30)
<span style="font-weight:bold;"> Key Exchange </span>    (weighted)  100 (30)
<span style="font-weight:bold;"> Cipher Strength </span> (weighted)  90 (36)
<span style="font-weight:bold;"> Final Score                  </span>96
<span style="font-weight:bold;"> Overall Grade                </span><span style="color:#008817;font-weight:bold;">A</span>
<span style="font-weight:bold;"> Grade cap reasons            </span>Grade capped to A. HSTS is not offered

<span style="color:white;background-color:black;"> Done 2025-09-19 12:44:19 [ 100s] --&gt;&gt; *************:443 (mbhbank.hu) &lt;&lt;--</span>

--------------------------------------------------------------------------------------------------------------------------
<span style="font-weight:bold;">Done testing now all IP addresses (on port 443): </span>********** *************

</pre>
</body>
</html>
