[{"id": "engine_problem", "ip": "/", "port": "443", "severity": "WARN", "finding": "No engine or GOST support via engine with your /usr/bin/openssl"}, {"id": "service", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "HTTP"}, {"id": "pre_128cipher", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No 128 cipher limit bug"}, {"id": "SSLv2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "not offered"}, {"id": "SSLv3", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "not offered"}, {"id": "TLS1", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "TLS1_1", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "TLS1_2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "TLS1_3", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "not offered + downgraded to weaker protocol"}, {"id": "NPN", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "offered with h2, http/1.1 (advertised)"}, {"id": "ALPN_HTTP2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "h2"}, {"id": "ALPN", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "http/1.1"}, {"id": "cipherlist_NULL", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_aNULL", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_EXPORT", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_LOW", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_3DES_IDEA", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "cwe": "CWE-310", "finding": "not offered"}, {"id": "cipherlist_OBSOLETED", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "cwe": "CWE-310", "finding": "not offered"}, {"id": "cipherlist_STRONG_NOFS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "cipherlist_STRONG_FS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "cipher_order-tls1_2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "server"}, {"id": "cipher-tls1_2_xc02b", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "TLSv1.2   xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH 256   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"}, {"id": "cipher-tls1_2_xc02c", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "TLSv1.2   xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH 256   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"}, {"id": "cipherorder_TLSv1_2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "ECDHE-ECDSA-AES128-GCM-SHA256 ECDHE-ECDSA-AES256-GCM-SHA384"}, {"id": "cipher_order", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "server"}, {"id": "FS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "FS_ciphers", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "FS_ECDHE_curves", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "prime256v1 secp384r1 secp521r1"}, {"id": "FS_TLS12_sig_algs", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "LOW", "finding": "ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 ECDSA+SHA1"}, {"id": "TLS_extensions", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "'server name/#0' 'EC point formats/#11' 'renegotiation info/#65281' 'session ticket/#35' 'next protocol/#13172' 'application layer protocol negotiation/#16' 'extended master secret/#23'"}, {"id": "TLS_session_ticket", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "valid for 86400 seconds only (<daily)"}, {"id": "SSL_sessionID_support", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "yes"}, {"id": "sessionresumption_ticket", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "supported"}, {"id": "sessionresumption_ID", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "supported"}, {"id": "TLS_timestamp", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "random"}, {"id": "cert_compression", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "N/A"}, {"id": "clientAuth", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "none"}, {"id": "cert_numbers", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "1"}, {"id": "cert_signatureAlgorithm", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "ECDSA with SHA256"}, {"id": "cert_keySize", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "EC 256 bits (curve P-256)"}, {"id": "cert_keyUsage", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "Digital Signature, Key Agreement"}, {"id": "cert_extKeyUsage", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLS Web Client Authentication, TLS Web Server Authentication"}, {"id": "cert_serialNumber", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "0588420F6A087A512BE3BB4BF10A"}, {"id": "cert_serialNumberLen", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "14"}, {"id": "cert_fingerprintSHA1", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "EE1B00E3DBFC6295DB87703885AA89A328EF9206"}, {"id": "cert_fingerprintSHA256", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845"}, {"id": "cert", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\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\n-----END CERTIFICATE-----"}, {"id": "cert_commonName", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "mbhbank.hu"}, {"id": "cert_commonName_wo_SNI", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu"}, {"id": "cert_subjectAltName", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu"}, {"id": "cert_trust", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "Ok via SAN (same w/o SNI)"}, {"id": "cert_chain_of_trust", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "CRITICAL", "finding": "Some certificate trust checks failed -> Java (self signed CA in chain)  , OK -> Mozilla Microsoft Linux Apple"}, {"id": "cert_certificatePolicies_EV", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "no"}, {"id": "cert_expirationStatus", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "289 >= 60 days"}, {"id": "cert_notBefore", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "2025-06-04 12:40"}, {"id": "cert_notAfter", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "2026-07-05 12:40"}, {"id": "cert_extlifeSpan", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "certificate has no extended life time according to browser forum"}, {"id": "cert_eTLS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "not present"}, {"id": "cert_crlDistributionPoints", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "http://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl"}, {"id": "cert_ocspURL", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "http://ec3sslca2017-ocsp1.e-szigno.hu http://ec3sslca2017-ocsp2.e-szigno.hu http://ec3sslca2017-ocsp3.e-szigno.hu"}, {"id": "OCSP_stapling", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "LOW", "finding": "not offered"}, {"id": "cert_mustStapleExtension", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "--"}, {"id": "DNS_CAArecord", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "LOW", "finding": "--"}, {"id": "certificate_transparency", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "yes (certificate extension)"}, {"id": "certs_countServer", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "4"}, {"id": "certs_list_ordering_problem", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "LOW", "finding": "yes"}, {"id": "cert_caIssuers", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "e-Szigno Class3 SSL CA 2017 (Microsec Ltd. from HU)"}, {"id": "intermediate_cert <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\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\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845"}, {"id": "intermediate_cert_notBefore <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "2025-06-04 12:40"}, {"id": "intermediate_cert_notAfter <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "2026-07-05 12:40"}, {"id": "intermediate_cert_expiration <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#1>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu <--  e-Szigno Class3 SSL CA 2017"}, {"id": "intermediate_cert <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\nMIID9zCCAt+gAwIBAgINAPRbZWA2kP3Mh3VlCjANBgkqhkiG9w0BAQsFADCBgjELMAkGA1UEBhMCSFUxETAPBgNVBAcMCEJ1ZGFwZXN0MRYwFAYDVQQKDA1NaWNyb3NlYyBMdGQuMScwJQYDVQQDDB5NaWNyb3NlYyBlLVN6aWdubyBSb290IENBIDIwMDkxHzAdBgkqhkiG9w0BCQEWEGluZm9AZS1zemlnbm8uaHUwHhcNMjMwODI0MTYwMDAwWhcNMjUxMjMwMTcwMDAwWjB3MQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xFzAVBgNVBGEMDlZBVEhVLTIzNTg0NDk3MSQwIgYDVQQDDBtlLVN6aWdubyBDbGFzczMgU1NMIENBIDIwMTcwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAStb9h8yBr4lF4CCD1Xl8tlJEReTfLNHTQ/KfIwWxg2eX2XQOcU6urnTbo0U1lYtm6eWUkIGIGWLFNgPQc8jWf3o4IBPzCCATswDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBEGA1UdIAQKMAgwBgYEVR0gADAdBgNVHQ4EFgQUiqU1uJLVprz5zoHqp5Yce94g5+AwHwYDVR0jBBgwFoAUyw/G30JDzD3LtUgjoRp6piq7NGgwNgYDVR0fBC8wLTAroCmgJ4YlaHR0cDovL2NybC5lLXN6aWduby5odS9yb290Y2EyMDA5LmNybDBuBggrBgEFBQcBAQRiMGAwKwYIKwYBBQUHMAGGH2h0dHA6Ly9yb290b2NzcDIwMDkuZS1zemlnbm8uaHUwMQYIKwYBBQUHMAKGJWh0dHA6Ly93d3cuZS1zemlnbm8uaHUvcm9vdGNhMjAwOS5jcnQwDQYJKoZIhvcNAQELBQADggEBAArvJY3rNJvCMdL3sqLO9EuD3lHkL8BxLtaU8MYk/mMz/Dsc0m+8raey8FxOvEerhGWvnDIil7a81FS5vAiP+vmbFvBzcqBfCPZec1wBu///TfYDHLoc7W53SrXlTrmHsP6csDBb2aeLHZoaSPZihOw4ZlAk6sJOUjFspHewneSkXUrC8xvg67ZRwSSqTYZEzK8fl7YwCLY+yOf9lm4gBmNLJGTe+tKIvCcLUTHrm8ltTReg1o0NXMXEWrxi1LexgPq+U+lFr646JJrEz2WGOzSOMaUvpnMHvM+ZDTkidAovTvNnW62pp6Ul17eQqfrgdkOW4tUuKTHC8fDCwPv26Mc=\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "1744D73134F95CE916ADEBEE6F75742C47936868B64D2A0C162EF132900F0EE4"}, {"id": "intermediate_cert_notBefore <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "2023-08-24 16:00"}, {"id": "intermediate_cert_notAfter <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "2025-12-30 17:00"}, {"id": "intermediate_cert_expiration <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#2>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "e-Szigno Class3 SSL CA 2017 <--  <EMAIL>"}, {"id": "intermediate_cert <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIECjCCAvKgAwIBAgIJAMJ+QwRORz8ZMA0GCSqGSIb3DQEBCwUAMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTAeFw0wOTA2MTYxMTMwMThaFw0yOTEyMzAxMTMwMThaMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOn4j/NjrdqG2KfgQvvPkd6mJviZpWNwrZuuyjNAfW2WbqEORO7hE52UQlKavXWFdCyoDh2Tthi3jCyoz/tccbna7P7ofo/kLx2yqHWH2Leh5TvPmUpG0IMZfcChEhyVbUr02MelTTMuhTlAdX4UfIASmFDHQWe4oIBhVKZsTh/gnQ4H6cm6M+f+wFUoLAKApxn1ntxVUwOXewdI/5n7N4okxFnMUBBjjqqpGrCEGob5X7uxUG6k0QrM1XF+H6cbfPVTbiJfyyvm1HxdrtbCxkzlBQHZ7Vf8wSN5/PrIJIOV87VqUQHQd9bpEqH5GoP7ghu5sJf0dgYzQ0mg/wu1+rUCAwEAAaOBgDB+MA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQDAgEGMB0GA1UdDgQWBBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAfBgNVHSMEGDAWgBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAbBgNVHREEFDASgRBpbmZvQGUtc3ppZ25vLmh1MA0GCSqGSIb3DQEBCwUAA4IBAQDJ0Q5eLtXMs3w+y/w9/w0olZMEyL/azXm4Q5DwpL7v8u8hmLzU1F0G9u5C7DBsoKqpyvGvivo/C3NqPuouQH4frlRheesuCDfXI/OMn74dseGkddug4lQUsbocKaQY9hK6ohQU4zE1yED/t+AFdlfBHFny+L/k7SViXITwfn4fs775tyERzAMBVnCnEJIeGzSBHq2cGsMEPO0CYdYeBvNfOofyK/FFh+U9rNHHV4S9a67c2Pm2G2JwCz02yULyMtd6YebS2z3PyKnJm9zbWETXbzivf3jTo60adbocwTZ8jx5tHMN1Rq41Bab2XD0h7lbwyYIiLXpUq3DDfSJlgnCW\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "3C5F81FEA5FAB82C64BFA2EAECAFCDE8E077FC8620A7CAE537163DF36EDBF378"}, {"id": "intermediate_cert_notBefore <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "2009-06-16 11:30"}, {"id": "intermediate_cert_notAfter <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "2029-12-30 11:30"}, {"id": "intermediate_cert_expiration <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#3>", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "<EMAIL> <--  <EMAIL>"}, {"id": "intermediate_cert_badOCSP", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "intermediate certificate(s) is/are ok"}, {"id": "HTTP_status_code", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "301 Moved Permanently ('/')"}, {"id": "HTTP_clock_skew", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "0 seconds from localtime"}, {"id": "HTTP_headerTime", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "**********"}, {"id": "HSTS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "LOW", "finding": "not offered"}, {"id": "HPKP", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No support for HTTP Public Key Pinning"}, {"id": "banner_server", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "awselb/2.0"}, {"id": "banner_application", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No application banner found"}, {"id": "cookie_count", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "0 at '/' (30x detected, better try target URL of 30x)"}, {"id": "security_headers", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "MEDIUM", "finding": "--"}, {"id": "banner_reverseproxy", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "cwe": "CWE-200", "finding": "--"}, {"id": "heartbleed", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2014-0160", "cwe": "CWE-119", "finding": "not vulnerable, no heartbeat extension"}, {"id": "CCS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2014-0224", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "ticketbleed", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2016-9244", "cwe": "CWE-200", "finding": "not vulnerable"}, {"id": "ROBOT", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2017-17382 CVE-2017-17427 CVE-2017-17428 CVE-2017-13098 CVE-2017-1000385 CVE-2017-13099 CVE-2016-6883 CVE-2012-5081 CVE-2017-6168", "cwe": "CWE-203", "finding": "not vulnerable, no RSA key transport cipher"}, {"id": "secure_renego", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cwe": "CWE-310", "finding": "supported"}, {"id": "secure_client_renego", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2011-1473", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "CRIME_TLS", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2012-4929", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "BREACH", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2013-3587", "cwe": "CWE-310", "finding": "not vulnerable, no gzip/deflate/compress/br HTTP compression  - only supplied '/' tested"}, {"id": "POODLE_SSL", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2014-3566", "cwe": "CWE-310", "finding": "not vulnerable, no SSLv3"}, {"id": "fallback_SCSV", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "no protocol below TLS 1.2 offered"}, {"id": "SWEET32", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2016-2183 CVE-2016-6329", "cwe": "CWE-327", "finding": "not vulnerable"}, {"id": "FREAK", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2015-0204", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "DROWN", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2016-0800 CVE-2016-0703", "cwe": "CWE-310", "finding": "not vulnerable on this host and port"}, {"id": "DROWN_hint", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "cve": "CVE-2016-0800 CVE-2016-0703", "cwe": "CWE-310", "finding": "no RSA certificate, can't be used with SSLv2 elsewhere"}, {"id": "LOGJAM", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2015-4000", "cwe": "CWE-310", "finding": "not vulnerable, no DH EXPORT ciphers,"}, {"id": "LOGJAM-common_primes", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2015-4000", "cwe": "CWE-310", "finding": "no DH key with <= TLS 1.2"}, {"id": "BEAST", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2011-3389", "cwe": "CWE-20", "finding": "not vulnerable, no SSL3 or TLS1"}, {"id": "LUCKY13", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2013-0169", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "winshock", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2014-6321", "cwe": "CWE-94", "finding": "not vulnerable - doesn't seem to be IIS 8.x"}, {"id": "RC4", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "cve": "CVE-2013-2566 CVE-2015-2808", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "clientsimulation-android_60", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_70", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_81", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_90", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_X", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_11", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_12", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-chrome_79_win10", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-chrome_101_win10", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-firefox_66_win81", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-firefox_100_win10", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_6_xp", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_8_win7", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_8_xp", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_11_win7", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_win81", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_winphone81", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_win10", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-edge_15_win10", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-edge_101_win10_21h2", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_121_ios_122", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_130_osx_10146", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_154_osx_1231", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java_7u25", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-java_8u161", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java1102", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java1703", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-go_1178", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-libressl_283", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_102e", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_110l", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_111d", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_303", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-apple_mail_16_0", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-thunderbird_91_9", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "rating_spec", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)"}, {"id": "rating_doc", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide"}, {"id": "protocol_support_score", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "100"}, {"id": "protocol_support_score_weighted", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "30"}, {"id": "key_exchange_score", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "100"}, {"id": "key_exchange_score_weighted", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "30"}, {"id": "cipher_strength_score", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "90"}, {"id": "cipher_strength_score_weighted", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "36"}, {"id": "final_score", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "96"}, {"id": "overall_grade", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "OK", "finding": "A"}, {"id": "grade_cap_reason_1", "ip": "www.budapestbank.hu/*************", "port": "443", "severity": "INFO", "finding": "Grade capped to A. HSTS is not offered"}, {"id": "service", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "HTTP"}, {"id": "pre_128cipher", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No 128 cipher limit bug"}, {"id": "SSLv2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "not offered"}, {"id": "SSLv3", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "not offered"}, {"id": "TLS1", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "TLS1_1", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "TLS1_2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "TLS1_3", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "not offered + downgraded to weaker protocol"}, {"id": "NPN", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "offered with h2, http/1.1 (advertised)"}, {"id": "ALPN_HTTP2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "h2"}, {"id": "ALPN", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "http/1.1"}, {"id": "cipherlist_NULL", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_aNULL", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_EXPORT", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_LOW", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cwe": "CWE-327", "finding": "not offered"}, {"id": "cipherlist_3DES_IDEA", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "cwe": "CWE-310", "finding": "not offered"}, {"id": "cipherlist_OBSOLETED", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "cwe": "CWE-310", "finding": "not offered"}, {"id": "cipherlist_STRONG_NOFS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "not offered"}, {"id": "cipherlist_STRONG_FS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "cipher_order-tls1_2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "server"}, {"id": "cipher-tls1_2_xc02b", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "TLSv1.2   xc02b   ECDHE-ECDSA-AES128-GCM-SHA256     ECDH 256   AESGCM      128      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"}, {"id": "cipher-tls1_2_xc02c", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "TLSv1.2   xc02c   ECDHE-ECDSA-AES256-GCM-SHA384     ECDH 256   AESGCM      256      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"}, {"id": "cipherorder_TLSv1_2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "ECDHE-ECDSA-AES128-GCM-SHA256 ECDHE-ECDSA-AES256-GCM-SHA384"}, {"id": "cipher_order", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "server"}, {"id": "FS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "offered"}, {"id": "FS_ciphers", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "ECDHE-ECDSA-AES256-GCM-SHA384 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "FS_ECDHE_curves", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "prime256v1 secp384r1 secp521r1"}, {"id": "FS_TLS12_sig_algs", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "LOW", "finding": "ECDSA+SHA256 ECDSA+SHA384 ECDSA+SHA512 ECDSA+SHA224 ECDSA+SHA1"}, {"id": "TLS_extensions", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "'server name/#0' 'EC point formats/#11' 'renegotiation info/#65281' 'session ticket/#35' 'next protocol/#13172' 'application layer protocol negotiation/#16' 'extended master secret/#23'"}, {"id": "TLS_session_ticket", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "valid for 86400 seconds only (<daily)"}, {"id": "SSL_sessionID_support", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "yes"}, {"id": "sessionresumption_ticket", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "supported"}, {"id": "sessionresumption_ID", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "supported"}, {"id": "TLS_timestamp", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "random"}, {"id": "cert_compression", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "N/A"}, {"id": "clientAuth", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "none"}, {"id": "cert_numbers", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "1"}, {"id": "cert_signatureAlgorithm", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "ECDSA with SHA256"}, {"id": "cert_keySize", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "EC 256 bits (curve P-256)"}, {"id": "cert_keyUsage", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "Digital Signature, Key Agreement"}, {"id": "cert_extKeyUsage", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLS Web Client Authentication, TLS Web Server Authentication"}, {"id": "cert_serialNumber", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "0588420F6A087A512BE3BB4BF10A"}, {"id": "cert_serialNumberLen", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "14"}, {"id": "cert_fingerprintSHA1", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "EE1B00E3DBFC6295DB87703885AA89A328EF9206"}, {"id": "cert_fingerprintSHA256", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845"}, {"id": "cert", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\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\n-----END CERTIFICATE-----"}, {"id": "cert_commonName", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "mbhbank.hu"}, {"id": "cert_commonName_wo_SNI", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu"}, {"id": "cert_subjectAltName", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu www.mbhbank.hu mkb.hu www.mkb.hu budapestbank.hu www.budapestbank.hu takarekbank.hu www.takarekbank.hu"}, {"id": "cert_trust", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "Ok via SAN (same w/o SNI)"}, {"id": "cert_chain_of_trust", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "CRITICAL", "finding": "Some certificate trust checks failed -> Java (self signed CA in chain)  , OK -> Mozilla Microsoft Linux Apple"}, {"id": "cert_certificatePolicies_EV", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "no"}, {"id": "cert_expirationStatus", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "289 >= 60 days"}, {"id": "cert_notBefore", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "2025-06-04 12:40"}, {"id": "cert_notAfter", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "2026-07-05 12:40"}, {"id": "cert_extlifeSpan", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "certificate has no extended life time according to browser forum"}, {"id": "cert_eTLS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "not present"}, {"id": "cert_crlDistributionPoints", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "http://ec3sslca2017-crl1.e-szigno.hu/ec3sslca2017.crl http://ec3sslca2017-crl2.e-szigno.hu/ec3sslca2017.crl http://ec3sslca2017-crl3.e-szigno.hu/ec3sslca2017.crl"}, {"id": "cert_ocspURL", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "http://ec3sslca2017-ocsp1.e-szigno.hu http://ec3sslca2017-ocsp2.e-szigno.hu http://ec3sslca2017-ocsp3.e-szigno.hu"}, {"id": "OCSP_stapling", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "LOW", "finding": "not offered"}, {"id": "cert_mustStapleExtension", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "--"}, {"id": "DNS_CAArecord", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "LOW", "finding": "--"}, {"id": "certificate_transparency", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "yes (certificate extension)"}, {"id": "certs_countServer", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "4"}, {"id": "certs_list_ordering_problem", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "LOW", "finding": "yes"}, {"id": "cert_caIssuers", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "e-Szigno Class3 SSL CA 2017 (Microsec Ltd. from HU)"}, {"id": "intermediate_cert <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\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\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "B765AD648B478DFBD6B3918F3478829DED1E240F40FE58CB9C5EEA98AFD13845"}, {"id": "intermediate_cert_notBefore <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "2025-06-04 12:40"}, {"id": "intermediate_cert_notAfter <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "2026-07-05 12:40"}, {"id": "intermediate_cert_expiration <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#1>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "mbhbank.hu <--  e-Szigno Class3 SSL CA 2017"}, {"id": "intermediate_cert <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "-----B<PERSON><PERSON> CERTIFICATE-----\nMIID9zCCAt+gAwIBAgINAPRbZWA2kP3Mh3VlCjANBgkqhkiG9w0BAQsFADCBgjELMAkGA1UEBhMCSFUxETAPBgNVBAcMCEJ1ZGFwZXN0MRYwFAYDVQQKDA1NaWNyb3NlYyBMdGQuMScwJQYDVQQDDB5NaWNyb3NlYyBlLVN6aWdubyBSb290IENBIDIwMDkxHzAdBgkqhkiG9w0BCQEWEGluZm9AZS1zemlnbm8uaHUwHhcNMjMwODI0MTYwMDAwWhcNMjUxMjMwMTcwMDAwWjB3MQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xFzAVBgNVBGEMDlZBVEhVLTIzNTg0NDk3MSQwIgYDVQQDDBtlLVN6aWdubyBDbGFzczMgU1NMIENBIDIwMTcwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAStb9h8yBr4lF4CCD1Xl8tlJEReTfLNHTQ/KfIwWxg2eX2XQOcU6urnTbo0U1lYtm6eWUkIGIGWLFNgPQc8jWf3o4IBPzCCATswDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMBEGA1UdIAQKMAgwBgYEVR0gADAdBgNVHQ4EFgQUiqU1uJLVprz5zoHqp5Yce94g5+AwHwYDVR0jBBgwFoAUyw/G30JDzD3LtUgjoRp6piq7NGgwNgYDVR0fBC8wLTAroCmgJ4YlaHR0cDovL2NybC5lLXN6aWduby5odS9yb290Y2EyMDA5LmNybDBuBggrBgEFBQcBAQRiMGAwKwYIKwYBBQUHMAGGH2h0dHA6Ly9yb290b2NzcDIwMDkuZS1zemlnbm8uaHUwMQYIKwYBBQUHMAKGJWh0dHA6Ly93d3cuZS1zemlnbm8uaHUvcm9vdGNhMjAwOS5jcnQwDQYJKoZIhvcNAQELBQADggEBAArvJY3rNJvCMdL3sqLO9EuD3lHkL8BxLtaU8MYk/mMz/Dsc0m+8raey8FxOvEerhGWvnDIil7a81FS5vAiP+vmbFvBzcqBfCPZec1wBu///TfYDHLoc7W53SrXlTrmHsP6csDBb2aeLHZoaSPZihOw4ZlAk6sJOUjFspHewneSkXUrC8xvg67ZRwSSqTYZEzK8fl7YwCLY+yOf9lm4gBmNLJGTe+tKIvCcLUTHrm8ltTReg1o0NXMXEWrxi1LexgPq+U+lFr646JJrEz2WGOzSOMaUvpnMHvM+ZDTkidAovTvNnW62pp6Ul17eQqfrgdkOW4tUuKTHC8fDCwPv26Mc=\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "1744D73134F95CE916ADEBEE6F75742C47936868B64D2A0C162EF132900F0EE4"}, {"id": "intermediate_cert_notBefore <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "2023-08-24 16:00"}, {"id": "intermediate_cert_notAfter <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "2025-12-30 17:00"}, {"id": "intermediate_cert_expiration <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#2>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "e-Szigno Class3 SSL CA 2017 <--  <EMAIL>"}, {"id": "intermediate_cert <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIECjCCAvKgAwIBAgIJAMJ+QwRORz8ZMA0GCSqGSIb3DQEBCwUAMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTAeFw0wOTA2MTYxMTMwMThaFw0yOTEyMzAxMTMwMThaMIGCMQswCQYDVQQGEwJIVTERMA8GA1UEBwwIQnVkYXBlc3QxFjAUBgNVBAoMDU1pY3Jvc2VjIEx0ZC4xJzAlBgNVBAMMHk1pY3Jvc2VjIGUtU3ppZ25vIFJvb3QgQ0EgMjAwOTEfMB0GCSqGSIb3DQEJARYQaW5mb0BlLXN6aWduby5odTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOn4j/NjrdqG2KfgQvvPkd6mJviZpWNwrZuuyjNAfW2WbqEORO7hE52UQlKavXWFdCyoDh2Tthi3jCyoz/tccbna7P7ofo/kLx2yqHWH2Leh5TvPmUpG0IMZfcChEhyVbUr02MelTTMuhTlAdX4UfIASmFDHQWe4oIBhVKZsTh/gnQ4H6cm6M+f+wFUoLAKApxn1ntxVUwOXewdI/5n7N4okxFnMUBBjjqqpGrCEGob5X7uxUG6k0QrM1XF+H6cbfPVTbiJfyyvm1HxdrtbCxkzlBQHZ7Vf8wSN5/PrIJIOV87VqUQHQd9bpEqH5GoP7ghu5sJf0dgYzQ0mg/wu1+rUCAwEAAaOBgDB+MA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQDAgEGMB0GA1UdDgQWBBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAfBgNVHSMEGDAWgBTLD8bfQkPMPcu1SCOhGnqmKrs0aDAbBgNVHREEFDASgRBpbmZvQGUtc3ppZ25vLmh1MA0GCSqGSIb3DQEBCwUAA4IBAQDJ0Q5eLtXMs3w+y/w9/w0olZMEyL/azXm4Q5DwpL7v8u8hmLzU1F0G9u5C7DBsoKqpyvGvivo/C3NqPuouQH4frlRheesuCDfXI/OMn74dseGkddug4lQUsbocKaQY9hK6ohQU4zE1yED/t+AFdlfBHFny+L/k7SViXITwfn4fs775tyERzAMBVnCnEJIeGzSBHq2cGsMEPO0CYdYeBvNfOofyK/FFh+U9rNHHV4S9a67c2Pm2G2JwCz02yULyMtd6YebS2z3PyKnJm9zbWETXbzivf3jTo60adbocwTZ8jx5tHMN1Rq41Bab2XD0h7lbwyYIiLXpUq3DDfSJlgnCW\n-----END CERTIFICATE-----"}, {"id": "intermediate_cert_fingerprintSHA256 <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "3C5F81FEA5FAB82C64BFA2EAECAFCDE8E077FC8620A7CAE537163DF36EDBF378"}, {"id": "intermediate_cert_notBefore <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "2009-06-16 11:30"}, {"id": "intermediate_cert_notAfter <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "2029-12-30 11:30"}, {"id": "intermediate_cert_expiration <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "ok > 40 days"}, {"id": "intermediate_cert_chain <#3>", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "<EMAIL> <--  <EMAIL>"}, {"id": "intermediate_cert_badOCSP", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "intermediate certificate(s) is/are ok"}, {"id": "HTTP_status_code", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "301 Moved Permanently ('/')"}, {"id": "HTTP_clock_skew", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "0 seconds from localtime"}, {"id": "HTTP_headerTime", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "**********"}, {"id": "HSTS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "LOW", "finding": "not offered"}, {"id": "HPKP", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No support for HTTP Public Key Pinning"}, {"id": "banner_server", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "awselb/2.0"}, {"id": "banner_application", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No application banner found"}, {"id": "cookie_count", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "0 at '/' (30x detected, better try target URL of 30x)"}, {"id": "security_headers", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "MEDIUM", "finding": "--"}, {"id": "banner_reverseproxy", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "cwe": "CWE-200", "finding": "--"}, {"id": "heartbleed", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2014-0160", "cwe": "CWE-119", "finding": "not vulnerable, no heartbeat extension"}, {"id": "CCS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2014-0224", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "ticketbleed", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2016-9244", "cwe": "CWE-200", "finding": "not vulnerable"}, {"id": "ROBOT", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2017-17382 CVE-2017-17427 CVE-2017-17428 CVE-2017-13098 CVE-2017-1000385 CVE-2017-13099 CVE-2016-6883 CVE-2012-5081 CVE-2017-6168", "cwe": "CWE-203", "finding": "not vulnerable, no RSA key transport cipher"}, {"id": "secure_renego", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cwe": "CWE-310", "finding": "supported"}, {"id": "secure_client_renego", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2011-1473", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "CRIME_TLS", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2012-4929", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "BREACH", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2013-3587", "cwe": "CWE-310", "finding": "not vulnerable, no gzip/deflate/compress/br HTTP compression  - only supplied '/' tested"}, {"id": "POODLE_SSL", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2014-3566", "cwe": "CWE-310", "finding": "not vulnerable, no SSLv3"}, {"id": "fallback_SCSV", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "no protocol below TLS 1.2 offered"}, {"id": "SWEET32", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2016-2183 CVE-2016-6329", "cwe": "CWE-327", "finding": "not vulnerable"}, {"id": "FREAK", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2015-0204", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "DROWN", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2016-0800 CVE-2016-0703", "cwe": "CWE-310", "finding": "not vulnerable on this host and port"}, {"id": "DROWN_hint", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "cve": "CVE-2016-0800 CVE-2016-0703", "cwe": "CWE-310", "finding": "no RSA certificate, can't be used with SSLv2 elsewhere"}, {"id": "LOGJAM", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2015-4000", "cwe": "CWE-310", "finding": "not vulnerable, no DH EXPORT ciphers,"}, {"id": "LOGJAM-common_primes", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2015-4000", "cwe": "CWE-310", "finding": "no DH key with <= TLS 1.2"}, {"id": "BEAST", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2011-3389", "cwe": "CWE-20", "finding": "not vulnerable, no SSL3 or TLS1"}, {"id": "LUCKY13", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2013-0169", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "winshock", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2014-6321", "cwe": "CWE-94", "finding": "not vulnerable - doesn't seem to be IIS 8.x"}, {"id": "RC4", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "cve": "CVE-2013-2566 CVE-2015-2808", "cwe": "CWE-310", "finding": "not vulnerable"}, {"id": "clientsimulation-android_60", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_70", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_81", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_90", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_X", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_11", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-android_12", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-chrome_79_win10", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-chrome_101_win10", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-firefox_66_win81", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-firefox_100_win10", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_6_xp", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_8_win7", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_8_xp", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-ie_11_win7", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_win81", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_winphone81", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-ie_11_win10", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-edge_15_win10", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-edge_101_win10_21h2", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_121_ios_122", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_130_osx_10146", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-safari_154_osx_1231", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java_7u25", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "No connection"}, {"id": "clientsimulation-java_8u161", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java1102", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-java1703", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-go_1178", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-libressl_283", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_102e", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_110l", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_111d", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-openssl_303", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-apple_mail_16_0", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "clientsimulation-thunderbird_91_9", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "TLSv1.2 ECDHE-ECDSA-AES128-GCM-SHA256"}, {"id": "rating_spec", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "SSL Labs's 'SSL Server Rating Guide' (version 2009q from 2020-01-30)"}, {"id": "rating_doc", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "https://github.com/ssllabs/research/wiki/SSL-Server-Rating-Guide"}, {"id": "protocol_support_score", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "100"}, {"id": "protocol_support_score_weighted", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "30"}, {"id": "key_exchange_score", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "100"}, {"id": "key_exchange_score_weighted", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "30"}, {"id": "cipher_strength_score", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "90"}, {"id": "cipher_strength_score_weighted", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "36"}, {"id": "final_score", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "96"}, {"id": "overall_grade", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "OK", "finding": "A"}, {"id": "grade_cap_reason_1", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "Grade capped to A. HSTS is not offered"}, {"id": "scanTime", "ip": "www.budapestbank.hu/************", "port": "443", "severity": "INFO", "finding": "134"}]